{"name": "ai-resume-screener", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@google/generative-ai": "^0.15.0", "@langchain/community": "^0.2.0", "@langchain/google-genai": "^0.0.25", "file-saver": "^2.0.5", "jspdf": "^2.5.1", "langchain": "^0.2.0", "lucide-react": "^0.344.0", "mammoth": "^1.9.1", "multer": "^2.0.1", "pdf-parse": "^1.1.1", "pdfjs-dist": "^4.10.38", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.0", "recharts": "^2.8.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/file-saver": "^2.0.7", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}