import React, { useState } from 'react';
import { useApp } from '../context/AppContext';
import { 
  Trophy, 
  Download, 
  FileText, 
  Star, 
  Award,
  TrendingUp,
  Users,
  Shield,
  Brain,
  Eye,
  Filter,
  Search,
  Target,
  BarChart3,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, LineChart, Line } from 'recharts';
import { saveAs } from 'file-saver';

const ResultsPage = () => {
  const { state } = useApp();
  const [selectedResume, setSelectedResume] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('rank');
  const [showOnlyTopCandidates, setShowOnlyTopCandidates] = useState(false);

  const filteredResults = state.results
    .filter(resume => {
      const matchesSearch = resume.extractedData.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        resume.extractedData.skills.some(skill => 
          skill.toLowerCase().includes(searchTerm.toLowerCase())
        );
      
      const matchesFilter = showOnlyTopCandidates ? resume.isTopCandidate : true;
      
      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'rank':
          return a.rank - b.rank;
        case 'recruiterScore':
          return b.scores.recruiterScore - a.scores.recruiterScore;
        case 'analystScore':
          return b.scores.analystScore - a.scores.analystScore;
        case 'hrScore':
          return b.scores.hrScore - a.scores.hrScore;
        case 'recommenderScore':
          return b.scores.recommenderScore - a.scores.recommenderScore;
        default:
          return a.rank - b.rank;
      }
    });

  const topCandidates = state.results.filter(r => r.isTopCandidate).slice(0, 3);

  const downloadCSV = () => {
    const headers = [
      'Rank',
      'Name',
      'Email',
      'Phone',
      'Recruiter Score',
      'Analyst Score',
      'HR Score',
      'Recommender Score',
      'Final Score',
      'Skills',
      'Experience Years',
      'Education',
      'Strengths',
      'Weaknesses',
      'Final Recommendation'
    ];

    const csvContent = [
      headers.join(','),
      ...state.results.map((resume) => [
        resume.rank,
        `"${resume.extractedData.name || 'N/A'}"`,
        `"${resume.extractedData.email || 'N/A'}"`,
        `"${resume.extractedData.phone || 'N/A'}"`,
        resume.scores.recruiterScore.toFixed(2),
        resume.scores.analystScore.toFixed(2),
        resume.scores.hrScore.toFixed(2),
        resume.scores.recommenderScore.toFixed(2),
        ((resume.scores.recruiterScore + resume.scores.analystScore + resume.scores.hrScore + resume.scores.recommenderScore) / 4).toFixed(2),
        `"${resume.extractedData.skills.join('; ')}"`,
        resume.extractedData.totalExperienceYears,
        `"${resume.extractedData.education.join('; ')}"`,
        `"${resume.analysis.strengths.join('; ')}"`,
        `"${resume.analysis.weaknesses.join('; ')}"`,
        `"${resume.analysis.finalRecommendation.replace(/"/g, '""')}"`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, 'ai_resume_analysis_results.csv');
  };

  const downloadResume = (resume: any) => {
    const url = URL.createObjectURL(resume.file);
    const link = document.createElement('a');
    link.href = url;
    link.download = resume.file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-emerald-600 bg-emerald-100 border-emerald-200';
    if (score >= 70) return 'text-blue-600 bg-blue-100 border-blue-200';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100 border-yellow-200';
    return 'text-red-600 bg-red-100 border-red-200';
  };

  const getRankBadge = (rank: number) => {
    if (rank === 1) return { emoji: '🥇', color: 'from-yellow-400 to-yellow-500', text: 'text-yellow-800' };
    if (rank === 2) return { emoji: '🥈', color: 'from-slate-400 to-slate-500', text: 'text-slate-800' };
    if (rank === 3) return { emoji: '🥉', color: 'from-orange-400 to-orange-500', text: 'text-orange-800' };
    return { emoji: `#${rank}`, color: 'from-slate-300 to-slate-400', text: 'text-slate-700' };
  };

  // Chart data
  const chartData = topCandidates.map((resume, index) => ({
    name: resume.extractedData.name || `Candidate ${index + 1}`,
    'Recruiter': resume.scores.recruiterScore,
    'Analyst': resume.scores.analystScore,
    'HR': resume.scores.hrScore,
    'Recommender': resume.scores.recommenderScore
  }));

  const radarData = selectedResume ? 
    state.results.find(r => r.id === selectedResume) : 
    topCandidates[0];

  const radarChartData = radarData ? [
    { subject: 'Technical Skills', A: radarData.scores.recruiterScore, fullMark: 100 },
    { subject: 'Job Match', A: radarData.scores.analystScore, fullMark: 100 },
    { subject: 'Communication', A: radarData.scores.hrScore, fullMark: 100 },
    { subject: 'Overall Fit', A: radarData.scores.recommenderScore, fullMark: 100 },
    { subject: 'Experience', A: radarData.breakdown.experienceMatch, fullMark: 100 },
    { subject: 'Education', A: radarData.breakdown.educationMatch, fullMark: 100 }
  ] : [];

  if (!state.results.length) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="text-center bg-white rounded-2xl shadow-lg p-8">
          <BarChart3 className="w-16 h-16 text-slate-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-slate-900 mb-4">No Results Available</h2>
          <p className="text-slate-600">Please upload resumes and complete the analysis first.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-12 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="relative">
              <Trophy className="w-16 h-16 text-yellow-500" />
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center">
                <CheckCircle className="w-3 h-3 text-white" />
              </div>
            </div>
          </div>
          
          <h1 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
            AI Resume Analysis Results
          </h1>
          <p className="text-lg text-slate-600 mb-6 max-w-3xl mx-auto">
            Multi-agent AI analysis complete! {state.results.length} candidates analyzed with 
            enterprise-grade scoring and detailed insights for top {state.topN} candidates.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={downloadCSV}
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-xl hover:bg-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <Download className="w-4 h-4 mr-2" />
              Download Full Report (CSV)
            </button>
          </div>
        </div>

        {/* Top 3 Candidates */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-slate-900 mb-8 flex items-center">
            <Trophy className="w-6 h-6 mr-3 text-yellow-500" />
            Top 3 Recommended Candidates
            <span className="ml-3 px-3 py-1 bg-emerald-100 text-emerald-700 text-sm font-medium rounded-full">
              AI Analyzed
            </span>
          </h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            {topCandidates.map((resume, index) => {
              const badge = getRankBadge(resume.rank);
              const finalScore = (resume.scores.recruiterScore + resume.scores.analystScore + resume.scores.hrScore + resume.scores.recommenderScore) / 4;
              
              return (
                <div key={resume.id} className="bg-white rounded-2xl shadow-xl overflow-hidden border-2 border-slate-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                  <div className={`h-4 bg-gradient-to-r ${badge.color}`}></div>
                  
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center bg-gradient-to-r ${badge.color} ${badge.text} font-bold text-lg`}>
                          {badge.emoji}
                        </div>
                        <div>
                          <h3 className="font-bold text-slate-900 text-lg">
                            {resume.extractedData.name || `Candidate ${index + 1}`}
                          </h3>
                          <p className="text-sm text-slate-600">Rank #{resume.rank}</p>
                        </div>
                      </div>
                      
                      <div className={`px-4 py-2 rounded-full text-sm font-bold border ${getScoreColor(finalScore)}`}>
                        {finalScore.toFixed(1)}%
                      </div>
                    </div>

                    {/* Agent Scores */}
                    <div className="space-y-3 mb-6">
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-slate-600 flex items-center">
                          <Users className="w-4 h-4 mr-1 text-blue-500" />
                          Recruiter
                        </span>
                        <span className="font-medium">{resume.scores.recruiterScore.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-slate-600 flex items-center">
                          <Target className="w-4 h-4 mr-1 text-emerald-500" />
                          Analyst
                        </span>
                        <span className="font-medium">{resume.scores.analystScore.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-slate-600 flex items-center">
                          <Shield className="w-4 h-4 mr-1 text-purple-500" />
                          HR
                        </span>
                        <span className="font-medium">{resume.scores.hrScore.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-slate-600 flex items-center">
                          <Award className="w-4 h-4 mr-1 text-orange-500" />
                          Recommender
                        </span>
                        <span className="font-medium">{resume.scores.recommenderScore.toFixed(1)}%</span>
                      </div>
                    </div>

                    {/* Key Skills */}
                    <div className="mb-6">
                      <p className="text-sm text-slate-600 mb-2 font-medium">Key Skills:</p>
                      <div className="flex flex-wrap gap-1">
                        {resume.extractedData.skills.slice(0, 4).map((skill, skillIndex) => (
                          <span key={skillIndex} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-lg font-medium">
                            {skill}
                          </span>
                        ))}
                        {resume.extractedData.skills.length > 4 && (
                          <span className="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded-lg">
                            +{resume.extractedData.skills.length - 4} more
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Experience */}
                    <div className="mb-6">
                      <p className="text-sm text-slate-600 mb-1 font-medium">Experience:</p>
                      <p className="text-sm text-slate-800">{resume.extractedData.totalExperienceYears} years</p>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setSelectedResume(selectedResume === resume.id ? null : resume.id)}
                        className="flex-1 px-4 py-2 bg-slate-100 hover:bg-slate-200 text-slate-700 font-medium rounded-lg transition-colors duration-200 flex items-center justify-center"
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        View Analysis
                      </button>
                      <button
                        onClick={() => downloadResume(resume)}
                        className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 flex items-center justify-center"
                        title="Download Resume"
                      >
                        <Download className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Analytics Dashboard */}
        <div className="grid lg:grid-cols-2 gap-8 mb-12">
          {/* Score Comparison Chart */}
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-slate-900 mb-6 flex items-center">
              <BarChart3 className="w-5 h-5 mr-2 text-blue-600" />
              Multi-Agent Score Comparison
            </h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis domain={[0, 100]} />
                <Tooltip />
                <Bar dataKey="Recruiter" fill="#3B82F6" radius={[2, 2, 0, 0]} />
                <Bar dataKey="Analyst" fill="#10B981" radius={[2, 2, 0, 0]} />
                <Bar dataKey="HR" fill="#8B5CF6" radius={[2, 2, 0, 0]} />
                <Bar dataKey="Recommender" fill="#F59E0B" radius={[2, 2, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>

          {/* Radar Chart */}
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-slate-900 mb-6 flex items-center">
              <Target className="w-5 h-5 mr-2 text-emerald-600" />
              Candidate Profile - {selectedResume ? 
                state.results.find(r => r.id === selectedResume)?.extractedData.name || 'Selected' :
                'Top Candidate'
              }
            </h3>
            <ResponsiveContainer width="100%" height={300}>
              <RadarChart data={radarChartData}>
                <PolarGrid />
                <PolarAngleAxis dataKey="subject" />
                <PolarRadiusAxis angle={90} domain={[0, 100]} />
                <Radar name="Score" dataKey="A" stroke="#3B82F6" fill="#3B82F6" fillOpacity={0.6} />
              </RadarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* All Candidates Table */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
          <div className="p-6 border-b border-slate-200">
            <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
              <div>
                <h2 className="text-xl font-bold text-slate-900 flex items-center">
                  <Users className="w-5 h-5 mr-2 text-blue-600" />
                  All Candidates ({filteredResults.length})
                </h2>
                <p className="text-sm text-slate-600 mt-1">
                  Complete ranking with multi-agent analysis
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search candidates..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full sm:w-64"
                  />
                </div>
                
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="rank">Rank</option>
                  <option value="recruiterScore">Recruiter Score</option>
                  <option value="analystScore">Analyst Score</option>
                  <option value="hrScore">HR Score</option>
                  <option value="recommenderScore">Recommender Score</option>
                </select>

                <label className="flex items-center space-x-2 px-4 py-2 bg-slate-50 rounded-lg">
                  <input
                    type="checkbox"
                    checked={showOnlyTopCandidates}
                    onChange={(e) => setShowOnlyTopCandidates(e.target.checked)}
                    className="rounded border-slate-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-slate-700">Top candidates only</span>
                </label>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-slate-50">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-slate-900">Rank</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-slate-900">Candidate</th>
                  <th className="px-6 py-4 text-center text-sm font-semibold text-slate-900">Recruiter</th>
                  <th className="px-6 py-4 text-center text-sm font-semibold text-slate-900">Analyst</th>
                  <th className="px-6 py-4 text-center text-sm font-semibold text-slate-900">HR</th>
                  <th className="px-6 py-4 text-center text-sm font-semibold text-slate-900">Recommender</th>
                  <th className="px-6 py-4 text-center text-sm font-semibold text-slate-900">Final</th>
                  <th className="px-6 py-4 text-center text-sm font-semibold text-slate-900">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-slate-200">
                {filteredResults.map((resume) => {
                  const badge = getRankBadge(resume.rank);
                  const finalScore = (resume.scores.recruiterScore + resume.scores.analystScore + resume.scores.hrScore + resume.scores.recommenderScore) / 4;
                  
                  return (
                    <React.Fragment key={resume.id}>
                      <tr className="hover:bg-slate-50 transition-colors duration-200">
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center bg-gradient-to-r ${badge.color} ${badge.text} font-bold text-sm mr-2`}>
                              {resume.rank <= 3 ? badge.emoji : resume.rank}
                            </div>
                            {resume.isTopCandidate && (
                              <Star className="w-4 h-4 text-yellow-500" />
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="font-semibold text-slate-900 flex items-center">
                              {resume.extractedData.name || `Candidate ${resume.rank}`}
                              {resume.isTopCandidate && (
                                <span className="ml-2 px-2 py-1 bg-emerald-100 text-emerald-700 text-xs font-medium rounded-full">
                                  AI Analyzed
                                </span>
                              )}
                            </div>
                            <div className="text-sm text-slate-600">
                              {resume.extractedData.email}
                            </div>
                            
                            <div className="text-xs text-slate-500 mt-1">
                              {resume.extractedData.totalExperienceYears} years exp • {resume.extractedData.skills.slice(0, 2).join(', ')}
                              {resume.extractedData.skills.length > 2 && ` +${resume.extractedData.skills.length - 2} more`}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-center">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(resume.scores.recruiterScore)}`}>
                            {resume.scores.recruiterScore.toFixed(1)}%
                          </span>
                        </td>
                        <td className="px-6 py-4 text-center">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(resume.scores.analystScore)}`}>
                            {resume.scores.analystScore.toFixed(1)}%
                          </span>
                        </td>
                        <td className="px-6 py-4 text-center">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(resume.scores.hrScore)}`}>
                            {resume.scores.hrScore.toFixed(1)}%
                          </span>
                        </td>
                        <td className="px-6 py-4 text-center">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(resume.scores.recommenderScore)}`}>
                            {resume.scores.recommenderScore.toFixed(1)}%
                          </span>
                        </td>
                        <td className="px-6 py-4 text-center">
                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-bold border ${getScoreColor(finalScore)}`}>
                            {finalScore.toFixed(1)}%
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex justify-center space-x-2">
                            <button
                              onClick={() => setSelectedResume(selectedResume === resume.id ? null : resume.id)}
                              className="p-2 text-slate-400 hover:text-slate-600 transition-colors duration-200"
                              title="View Details"
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => downloadResume(resume)}
                              className="p-2 text-blue-400 hover:text-blue-600 transition-colors duration-200"
                              title="Download Resume"
                            >
                              <Download className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                      
                      {/* Expanded Details */}
                      {selectedResume === resume.id && (
                        <tr>
                          <td colSpan={8} className="px-6 py-8 bg-slate-50">
                            <div className="max-w-6xl mx-auto">
                              <div className="grid lg:grid-cols-2 gap-8">
                                {/* AI Agent Analysis */}
                                <div>
                                  <h4 className="font-bold text-slate-900 mb-6 flex items-center">
                                    <Brain className="w-5 h-5 mr-2 text-blue-600" />
                                    Multi-Agent AI Analysis
                                  </h4>
                                  <div className="space-y-4">
                                    <div className="p-4 bg-white rounded-lg border border-blue-200">
                                      <div className="flex items-center mb-3">
                                        <Users className="w-4 h-4 mr-2 text-blue-600" />
                                        <span className="font-medium text-slate-900">Recruiter Agent</span>
                                        <span className="ml-auto text-sm font-bold text-blue-600">
                                          {resume.scores.recruiterScore.toFixed(1)}%
                                        </span>
                                      </div>
                                      <p className="text-sm text-slate-600">{resume.analysis.recruiterAnalysis}</p>
                                    </div>
                                    
                                    <div className="p-4 bg-white rounded-lg border border-emerald-200">
                                      <div className="flex items-center mb-3">
                                        <Target className="w-4 h-4 mr-2 text-emerald-600" />
                                        <span className="font-medium text-slate-900">Analyst Agent</span>
                                        <span className="ml-auto text-sm font-bold text-emerald-600">
                                          {resume.scores.analystScore.toFixed(1)}%
                                        </span>
                                      </div>
                                      <p className="text-sm text-slate-600">{resume.analysis.analystAnalysis}</p>
                                    </div>
                                    
                                    <div className="p-4 bg-white rounded-lg border border-purple-200">
                                      <div className="flex items-center mb-3">
                                        <Shield className="w-4 h-4 mr-2 text-purple-600" />
                                        <span className="font-medium text-slate-900">HR Agent</span>
                                        <span className="ml-auto text-sm font-bold text-purple-600">
                                          {resume.scores.hrScore.toFixed(1)}%
                                        </span>
                                      </div>
                                      <p className="text-sm text-slate-600">{resume.analysis.hrAnalysis}</p>
                                    </div>

                                    <div className="p-4 bg-white rounded-lg border border-orange-200">
                                      <div className="flex items-center mb-3">
                                        <Award className="w-4 h-4 mr-2 text-orange-600" />
                                        <span className="font-medium text-slate-900">Recommender Agent</span>
                                        <span className="ml-auto text-sm font-bold text-orange-600">
                                          {resume.scores.recommenderScore.toFixed(1)}%
                                        </span>
                                      </div>
                                      <p className="text-sm text-slate-600">{resume.analysis.recommenderAnalysis}</p>
                                    </div>
                                  </div>
                                </div>
                                
                                {/* Candidate Information */}
                                <div>
                                  <h4 className="font-bold text-slate-900 mb-6 flex items-center">
                                    <FileText className="w-5 h-5 mr-2 text-slate-600" />
                                    Extracted Information
                                  </h4>
                                  <div className="space-y-6">
                                    {/* Skills */}
                                    <div>
                                      <label className="text-sm font-medium text-slate-700 mb-2 block">Skills</label>
                                      <div className="flex flex-wrap gap-1">
                                        {resume.extractedData.skills.map((skill, skillIndex) => (
                                          <span key={skillIndex} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-lg">
                                            {skill}
                                          </span>
                                        ))}
                                      </div>
                                    </div>
                                    
                                    {/* Education */}
                                    <div>
                                      <label className="text-sm font-medium text-slate-700 mb-2 block">Education</label>
                                      <ul className="text-sm text-slate-600 space-y-1">
                                        {resume.extractedData.education.map((edu, eduIndex) => (
                                          <li key={eduIndex} className="flex items-start">
                                            <span className="text-slate-400 mr-2">•</span>
                                            <span>{edu}</span>
                                          </li>
                                        ))}
                                      </ul>
                                    </div>
                                    
                                    {/* Experience */}
                                    <div>
                                      <label className="text-sm font-medium text-slate-700 mb-2 block">
                                        Experience ({resume.extractedData.totalExperienceYears} years)
                                      </label>
                                      <ul className="text-sm text-slate-600 space-y-1">
                                        {resume.extractedData.workExperience.slice(0, 3).map((exp, expIndex) => (
                                          <li key={expIndex} className="flex items-start">
                                            <span className="text-slate-400 mr-2">•</span>
                                            <span>{exp.position} at {exp.company} ({exp.duration})</span>
                                          </li>
                                        ))}
                                      </ul>
                                    </div>

                                    {/* Experience */}
<div className="mb-6">
  <p className="text-sm text-slate-600 mb-1 font-medium">Experience:</p>
  <p className="text-sm text-slate-800">{resume.extractedData.totalExperienceYears} years</p>
</div>

{/* Summary - NEW */}
{resume.extractedData.summary && (
  <div className="mb-6">
    <p className="text-sm text-slate-600 mb-1 font-medium">Summary:</p>
    <p className="text-sm text-slate-800 line-clamp-2">{resume.extractedData.summary}</p>
  </div>
)}

{/* Certifications - NEW */}
{resume.extractedData.certifications && resume.extractedData.certifications.length > 0 && (
  <div className="mb-6">
    <p className="text-sm text-slate-600 mb-1 font-medium">Certifications:</p>
    <div className="flex flex-wrap gap-1">
      {resume.extractedData.certifications.slice(0, 2).map((cert, certIndex) => (
        <span key={certIndex} className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-lg font-medium">
          {cert}
        </span>
      ))}
      {resume.extractedData.certifications.length > 2 && (
        <span className="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded-lg">
          +{resume.extractedData.certifications.length - 2} more
        </span>
      )}
    </div>
  </div>
)}

{/* Projects - NEW */}
{resume.extractedData.projects && resume.extractedData.projects.length > 0 && (
  <div className="mb-6">
    <p className="text-sm text-slate-600 mb-1 font-medium">Projects:</p>
    <div className="flex flex-wrap gap-1">
      {resume.extractedData.projects.slice(0, 2).map((proj, projIndex) => (
        <span key={projIndex} className="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-lg font-medium">
          {proj}
        </span>
      ))}
      {resume.extractedData.projects.length > 2 && (
        <span className="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded-lg">
          +{resume.extractedData.projects.length - 2} more
        </span>
      )}
    </div>
  </div>
)}

{/* Education - NEW (formatted as badges like skills) */}
{resume.extractedData.education && resume.extractedData.education.length > 0 && (
  <div className="mb-6">
    <p className="text-sm text-slate-600 mb-1 font-medium">Education:</p>
    <div className="flex flex-wrap gap-1">
      {resume.extractedData.education.slice(0, 2).map((edu, eduIndex) => (
        <span key={eduIndex} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-lg font-medium">
          {edu}
        </span>
      ))}
      {resume.extractedData.education.length > 2 && (
        <span className="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded-lg">
          +{resume.extractedData.education.length - 2} more
        </span>
      )}
    </div>
  </div>
)}

{/* Action Buttons */}
<div className="flex space-x-2">
  ...
</div>


                                    {/* Strengths & Weaknesses */}
                                    <div className="grid md:grid-cols-2 gap-4">
                                      <div>
                                        <label className="text-sm font-medium text-emerald-700 mb-2 block flex items-center">
                                          <CheckCircle className="w-4 h-4 mr-1" />
                                          Strengths
                                        </label>
                                        <ul className="text-sm text-emerald-600 space-y-1">
                                          {resume.analysis.strengths.map((strength, index) => (
                                            <li key={index} className="flex items-start">
                                              <span className="text-emerald-400 mr-2">•</span>
                                              <span>{strength}</span>
                                            </li>
                                          ))}
                                        </ul>
                                      </div>
                                      
                                      <div>
                                        <label className="text-sm font-medium text-orange-700 mb-2 block flex items-center">
                                          <AlertTriangle className="w-4 h-4 mr-1" />
                                          Areas for Improvement
                                        </label>
                                        <ul className="text-sm text-orange-600 space-y-1">
                                          {resume.analysis.weaknesses.map((weakness, index) => (
                                            <li key={index} className="flex items-start">
                                              <span className="text-orange-400 mr-2">•</span>
                                              <span>{weakness}</span>
                                            </li>
                                          ))}
                                        </ul>
                                      </div>
                                    </div>
                                    
                                    {/* Final Recommendation */}
                                    <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                                      <div className="flex items-center mb-2">
                                        <Brain className="w-4 h-4 mr-2 text-blue-600" />
                                        <span className="font-medium text-blue-900">Final AI Recommendation</span>
                                      </div>
                                      <p className="text-sm text-blue-800">{resume.analysis.finalRecommendation}</p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResultsPage;