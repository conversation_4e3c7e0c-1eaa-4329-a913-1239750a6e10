import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useApp } from '../context/AppContext';
import { ArrowRight, FileText, Sparkles, Target, Users, Award, Briefcase } from 'lucide-react';
import { JobDescription } from '../types';

const JobDescriptionPage: React.FC = () => {
  const { state, dispatch } = useApp();
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState({
    title: '',
    company: '',
    description: state.jobDescriptionText || '',
    requiredSkills: '',
    preferredSkills: '',
    experience: '',
    education: '',
    responsibilities: ''
  });
  
  const [topN, setTopN] = useState(state.topN);
  const [customTopN, setCustomTopN] = useState('');
  const [isCustom, setIsCustom] = useState(false);

  const sampleJobDescription = {
    title: 'Senior Full Stack Developer',
    company: 'TechCorp Solutions',
    description: `We are seeking a highly skilled Senior Full Stack Developer to join our dynamic engineering team. The ideal candidate will have extensive experience in modern web technologies and a passion for building scalable, high-performance applications.`,
    requiredSkills: 'JavaScript, TypeScript, React, Node.js, Python, SQL, Git, AWS, Docker',
    preferredSkills: 'GraphQL, Kubernetes, MongoDB, Redis, CI/CD, Microservices, Machine Learning',
    experience: '5+ years of professional software development experience',
    education: 'Bachelor\'s degree in Computer Science, Engineering, or related field',
    responsibilities: `• Design and develop scalable web applications using modern frameworks
• Collaborate with cross-functional teams to define and implement new features
• Write clean, maintainable, and well-documented code
• Participate in code reviews and mentor junior developers
• Optimize application performance and ensure high availability
• Stay up-to-date with emerging technologies and industry best practices`
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleTopNSelection = (num: number) => {
    setTopN(num);
    setIsCustom(false);
    setCustomTopN('');
  };

  const handleCustomTopNChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setCustomTopN(value);
    setIsCustom(true);
    
    const numValue = parseInt(value);
    if (!isNaN(numValue) && numValue > 0 && numValue <= state.uploadedFiles.length) {
      setTopN(numValue);
    }
  };

  const useSampleDescription = () => {
    setFormData(sampleJobDescription);
  };

  const handleContinue = () => {
    if (!formData.title.trim() || !formData.description.trim()) {
      alert('Please fill in at least the job title and description');
      return;
    }

    const finalTopN = isCustom ? parseInt(customTopN) || 5 : topN;
    
    if (finalTopN > state.uploadedFiles.length) {
      alert(`You can only select up to ${state.uploadedFiles.length} resumes (total uploaded)`);
      return;
    }

    const jobDescription: JobDescription = {
      title: formData.title,
      company: formData.company,
      description: formData.description,
      requiredSkills: formData.requiredSkills.split(',').map(s => s.trim()).filter(Boolean),
      preferredSkills: formData.preferredSkills.split(',').map(s => s.trim()).filter(Boolean),
      experience: formData.experience,
      education: formData.education,
      responsibilities: formData.responsibilities.split('\n').map(s => s.trim()).filter(Boolean)
    };

    const fullJobText = `
      Job Title: ${jobDescription.title}
      Company: ${jobDescription.company}
      
      Description: ${jobDescription.description}
      
      Required Skills: ${jobDescription.requiredSkills.join(', ')}
      Preferred Skills: ${jobDescription.preferredSkills.join(', ')}
      
      Experience: ${jobDescription.experience}
      Education: ${jobDescription.education}
      
      Responsibilities:
      ${jobDescription.responsibilities.join('\n')}
    `.trim();

    dispatch({ type: 'SET_JOB_DESCRIPTION', payload: jobDescription });
    dispatch({ type: 'SET_JOB_DESCRIPTION_TEXT', payload: fullJobText });
    dispatch({ type: 'SET_TOP_N', payload: finalTopN });
    
    navigate('/extraction');
  };

  return (
    <div className="min-h-screen py-12 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="relative">
              <FileText className="w-16 h-16 text-blue-600" />
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center">
                <Target className="w-3 h-3 text-white" />
              </div>
            </div>
          </div>
          
          <h1 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
            Define Job Requirements
          </h1>
          <p className="text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Provide detailed job requirements to enable our AI agents to perform accurate candidate matching. 
            The more specific you are, the better our multi-agent system can rank candidates.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-8">
            {/* Basic Information */}
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-slate-900 flex items-center">
                  <Briefcase className="w-5 h-5 mr-2 text-blue-600" />
                  Basic Information
                </h2>
                <button
                  onClick={useSampleDescription}
                  className="flex items-center px-4 py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                >
                  <Sparkles className="w-4 h-4 mr-1" />
                  Use Sample
                </button>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Job Title *
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="e.g., Senior Full Stack Developer"
                    className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Company Name
                  </label>
                  <input
                    type="text"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    placeholder="e.g., TechCorp Solutions"
                    className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Job Description *
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Provide a detailed job description including role overview, key responsibilities, and what you're looking for in a candidate..."
                  rows={6}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
              </div>
            </div>

            {/* Skills & Requirements */}
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <h2 className="text-xl font-semibold text-slate-900 mb-6 flex items-center">
                <Target className="w-5 h-5 mr-2 text-blue-600" />
                Skills & Requirements
              </h2>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Required Skills (comma-separated)
                  </label>
                  <input
                    type="text"
                    name="requiredSkills"
                    value={formData.requiredSkills}
                    onChange={handleInputChange}
                    placeholder="JavaScript, React, Node.js, Python, SQL, AWS"
                    className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Preferred Skills (comma-separated)
                  </label>
                  <input
                    type="text"
                    name="preferredSkills"
                    value={formData.preferredSkills}
                    onChange={handleInputChange}
                    placeholder="GraphQL, Kubernetes, Docker, Machine Learning"
                    className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Experience Required
                    </label>
                    <input
                      type="text"
                      name="experience"
                      value={formData.experience}
                      onChange={handleInputChange}
                      placeholder="5+ years of professional experience"
                      className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Education Requirements
                    </label>
                    <input
                      type="text"
                      name="education"
                      value={formData.education}
                      onChange={handleInputChange}
                      placeholder="Bachelor's degree in Computer Science"
                      className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Responsibilities */}
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <h2 className="text-xl font-semibold text-slate-900 mb-6 flex items-center">
                <Users className="w-5 h-5 mr-2 text-blue-600" />
                Key Responsibilities
              </h2>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Responsibilities (one per line)
                </label>
                <textarea
                  name="responsibilities"
                  value={formData.responsibilities}
                  onChange={handleInputChange}
                  placeholder="• Design and develop scalable web applications&#10;• Collaborate with cross-functional teams&#10;• Write clean, maintainable code&#10;• Mentor junior developers"
                  rows={6}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
              </div>
            </div>

            {/* Continue Button */}
            <div className="text-center">
              <button
                onClick={handleContinue}
                disabled={!formData.title.trim() || !formData.description.trim()}
                className={`inline-flex items-center px-8 py-4 font-semibold rounded-xl transition-all duration-200 ${
                  formData.title.trim() && formData.description.trim()
                    ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-xl transform hover:-translate-y-1'
                    : 'bg-slate-300 text-slate-500 cursor-not-allowed'
                }`}
              >
                Start AI Analysis
                <ArrowRight className="w-5 h-5 ml-2" />
              </button>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Analysis Settings */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="font-semibold text-slate-900 mb-4 flex items-center">
                <Award className="w-5 h-5 mr-2 text-blue-600" />
                Analysis Settings
              </h3>
              
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-3">
                  Select number of top candidates for detailed AI feedback:
                </label>
                
                {/* Quick Selection Buttons */}
                <div className="grid grid-cols-2 gap-2 mb-4">
                  {[3, 5, 8, 10].map((num) => (
                    <button
                      key={num}
                      onClick={() => handleTopNSelection(num)}
                      disabled={num > state.uploadedFiles.length}
                      className={`px-3 py-2 rounded-lg font-medium transition-colors duration-200 text-sm ${
                        topN === num && !isCustom
                          ? 'bg-blue-600 text-white'
                          : num > state.uploadedFiles.length
                          ? 'bg-slate-100 text-slate-400 cursor-not-allowed'
                          : 'bg-slate-100 text-slate-700 hover:bg-slate-200'
                      }`}
                    >
                      Top {num}
                    </button>
                  ))}
                </div>

                {/* Custom Input */}
                <div className="border-t border-slate-200 pt-4">
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Or enter custom number:
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      min="1"
                      max={state.uploadedFiles.length}
                      value={customTopN}
                      onChange={handleCustomTopNChange}
                      placeholder="e.g., 7"
                      className={`flex-1 p-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm ${
                        isCustom ? 'ring-2 ring-blue-500 border-transparent' : ''
                      }`}
                    />
                    <span className="text-xs text-slate-500">
                      max {state.uploadedFiles.length}
                    </span>
                  </div>
                </div>

                <p className="text-xs text-slate-600 mt-3 bg-slate-50 p-2 rounded">
                  <strong>Selected:</strong> Top {isCustom ? (parseInt(customTopN) || 0) : topN} candidates will receive detailed multi-agent analysis. All resumes will be scored and ranked.
                </p>
              </div>
            </div>

            {/* Tips */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="font-semibold text-slate-900 mb-4">💡 Optimization Tips</h3>
              <ul className="text-sm text-slate-600 space-y-2">
                <li>• Include specific technical skills and tools</li>
                <li>• Mention required years of experience</li>
                <li>• List education requirements clearly</li>
                <li>• Describe key responsibilities in detail</li>
                <li>• Separate required vs. preferred qualifications</li>
                <li>• Use industry-standard terminology</li>
              </ul>
            </div>

            {/* Agent Preview */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="font-semibold text-slate-900 mb-4">🤖 AI Agent Focus</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"></div>
                  <div>
                    <span className="font-medium text-slate-900">Recruiter Agent:</span>
                    <span className="text-slate-600"> Skills & experience extraction</span>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 mr-2 flex-shrink-0"></div>
                  <div>
                    <span className="font-medium text-slate-900">Analyst Agent:</span>
                    <span className="text-slate-600"> Job requirement matching</span>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-2 flex-shrink-0"></div>
                  <div>
                    <span className="font-medium text-slate-900">HR Agent:</span>
                    <span className="text-slate-600"> Communication & soft skills</span>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-2 flex-shrink-0"></div>
                  <div>
                    <span className="font-medium text-slate-900">Recommender:</span>
                    <span className="text-slate-600"> Final ranking & insights</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Summary */}
            {state.uploadedFiles.length > 0 && (
              <div className="bg-blue-50 rounded-2xl p-6 border border-blue-200">
                <h3 className="font-semibold text-blue-900 mb-3">Analysis Summary</h3>
                <div className="text-sm text-blue-800 space-y-1">
                  <p>• {state.uploadedFiles.length} resume files ready</p>
                  <p>• Top {isCustom ? (parseInt(customTopN) || 0) : topN} candidates will get detailed AI feedback</p>
                  <p>• Multi-agent analysis with LangChain & RAG</p>
                  <p>• Enterprise-grade scoring algorithm</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobDescriptionPage;