import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { AppState, AppAction, JobDescription, ResumeResult } from '../types';

const initialState: AppState = {
  uploadedFiles: [],
  jobDescription: null,
  jobDescriptionText: '',
  results: [],
  isAnalyzing: false,
  analysisProgress: 0,
  currentStep: '',
  topN: 3,
  extractedContent: new Map(),
};

const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_UPLOADED_FILES':
      return { ...state, uploadedFiles: action.payload };
    case 'SET_JOB_DESCRIPTION':
      return { ...state, jobDescription: action.payload };
    case 'SET_JOB_DESCRIPTION_TEXT':
      return { ...state, jobDescriptionText: action.payload };
    case 'SET_RESULTS':
      return { ...state, results: action.payload };
    case 'SET_ANALYZING':
      return { ...state, isAnalyzing: action.payload };
    case 'SET_PROGRESS':
      return { 
        ...state, 
        analysisProgress: action.payload.progress,
        currentStep: action.payload.step
      };
    case 'SET_TOP_N':
      return { ...state, topN: action.payload };
    case 'SET_EXTRACTED_CONTENT':
      const newContent = new Map(state.extractedContent);
      newContent.set(action.payload.fileId, action.payload.content);
      return { ...state, extractedContent: newContent };
    case 'RESET_STATE':
      return initialState;
    default:
      return state;
  }
};

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
}>({
  state: initialState,
  dispatch: () => null,
});

export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
};

export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};