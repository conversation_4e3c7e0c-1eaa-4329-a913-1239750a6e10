// src/services/langGraphService.ts

/import { StateGraph, END } from "@langchain/langgraph";
import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";

// Graph State Interface
interface LangGraphState {
  candidateData: any;
  jobDescription: any;
  candidateId: string;
  recruiterResult?: any;
  analystResult?: any;
  hrResult?: any;
  recommenderResult?: any;
  finalResult?: any;
  messages: BaseMessage[];
}

// Recruiter Agent Node
const recruiterAgentNode = async (state: LangGraphState): Promise<Partial<LangGraphState>> => {
  const message = new HumanMessage(`Recruiter Agent processing: ${state.candidateData.name}`);
  
  const recruiterResult = {
    agentType: "recruiter",
    processed: true,
    timestamp: new Date().toISOString()
  };
  
  return {
    ...state,
    recruiterResult,
    messages: [...state.messages, message, new AIMessage("Recruiter node completed")]
  };
};

// Analyst Agent Node
const analystAgentNode = async (state: LangGraphState): Promise<Partial<LangGraphState>> => {
  const message = new HumanMessage(`Analyst Agent processing: ${state.candidateData.name}`);
  
  const analystResult = {
    agentType: "analyst",
    processed: true,
    timestamp: new Date().toISOString()
  };
  
  return {
    ...state,
    analystResult,
    messages: [...state.messages, message, new AIMessage("Analyst node completed")]
  };
};

// HR Agent Node
const hrAgentNode = async (state: LangGraphState): Promise<Partial<LangGraphState>> => {
  const message = new HumanMessage(`HR Agent processing: ${state.candidateData.name}`);
  
  const hrResult = {
    agentType: "hr",
    processed: true,
    timestamp: new Date().toISOString()
  };
  
  return {
    ...state,
    hrResult,
    messages: [...state.messages, message, new AIMessage("HR node completed")]
  };
};

// Recommender Agent Node
const recommenderAgentNode = async (state: LangGraphState): Promise<Partial<LangGraphState>> => {
  const message = new HumanMessage(`Recommender Agent processing: ${state.candidateData.name}`);
  
  const recommenderResult = {
    agentType: "recommender",
    processed: true,
    timestamp: new Date().toISOString()
  };
  
  return {
    ...state,
    recommenderResult,
    messages: [...state.messages, message, new AIMessage("Recommender node completed")]
  };
};

// Aggregator Node
const aggregatorNode = async (state: LangGraphState): Promise<Partial<LangGraphState>> => {
  const message = new HumanMessage(`Aggregating results for: ${state.candidateData.name}`);
  
  const finalResult = {
    candidateId: state.candidateId,
    allAgentsCompleted: true,
    processingComplete: true,
    timestamp: new Date().toISOString(),
    agentResults: {
      recruiter: state.recruiterResult,
      analyst: state.analystResult,
      hr: state.hrResult,
      recommender: state.recommenderResult
    }
  };
  
  return {
    ...state,
    finalResult,
    messages: [...state.messages, message, new AIMessage("Aggregation completed")]
  };
};

// Create LangGraph
export const createLangGraph = () => {
  const workflow = new StateGraph<LangGraphState>({
    channels: {
      candidateData: null,
      jobDescription: null,
      candidateId: null,
      recruiterResult: null,
      analystResult: null,
      hrResult: null,
      recommenderResult: null,
      finalResult: null,
      messages: null
    }
  });

  // Add all agent nodes
  workflow.addNode("recruiter_agent", recruiterAgentNode);
  workflow.addNode("analyst_agent", analystAgentNode);
  workflow.addNode("hr_agent", hrAgentNode);
  workflow.addNode("recommender_agent", recommenderAgentNode);
  workflow.addNode("aggregator", aggregatorNode);

  // Define edges (connections between nodes)
  workflow.setEntryPoint("recruiter_agent");
  workflow.addEdge("recruiter_agent", "analyst_agent");
  workflow.addEdge("analyst_agent", "hr_agent");
  workflow.addEdge("hr_agent", "recommender_agent");
  workflow.addEdge("recommender_agent", "aggregator");
  workflow.addEdge("aggregator", END);

  return workflow.compile();
};

// Execute LangGraph
export const executeLangGraph = async (
  candidateData: any,
  jobDescription: any,
  candidateId: string
) => {
  const graph = createLangGraph();
  
  const initialState: LangGraphState = {
    candidateData,
    jobDescription,
    candidateId,
    messages: [new HumanMessage(`LangGraph started for: ${candidateData.name || candidateId}`)]
  };
  
  const result = await graph.invoke(initialState);
  return result;
};

// LangGraph Service Class
export class LangGraphService {
  private graph: any;

  constructor() {
    this.graph = createLangGraph();
  }

  async processCandidate(candidateData: any, jobDescription: any, candidateId: string) {
    return await executeLangGraph(candidateData, jobDescription, candidateId);
  }

  getGraphStructure() {
    return {
      nodes: ["recruiter_agent", "analyst_agent", "hr_agent", "recommender_agent", "aggregator"],
      edges: [
        { from: "START", to: "recruiter_agent" },
        { from: "recruiter_agent", to: "analyst_agent" },
        { from: "analyst_agent", to: "hr_agent" },
        { from: "hr_agent", to: "recommender_agent" },
        { from: "recommender_agent", to: "aggregator" },
        { from: "aggregator", to: "END" }
      ]
    };
  }
}

export const langGraphService = new LangGraphService();
