import * as pdfjsLib from 'pdfjs-dist';
import mammoth from 'mammoth';

// Configure PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/4.0.379/pdf.worker.min.js`;

export interface ExtractedData {
  name: string;
  email: string;
  phone: string;
  skills: string[];
  education: string[];
  experience: string[];
  summary: string;
  certifications: string[];
  projects: string[];
  // PhD Scholar specific metadata
  publications: Publication[];
  conferences: Conference[];
  researchInterests: string[];
  awards: string[];
  grants: Grant[];
  teachingExperience: string[];
  supervision: string[];
  patents: string[];
  editorialRoles: string[];
  professionalMemberships: string[];
  languages: string[];
  researchProjects: ResearchProject[];
  thesisTitle?: string;
  advisor?: string;
  institution?: string;
  graduationYear?: number;
  hIndex?: number;
  citationCount?: number;
  totalScore?: number; // Overall scoring for ranking
}

export interface Publication {
  title: string;
  authors: string[];
  journal: string;
  year: number;
  type: 'journal' | 'conference' | 'book' | 'chapter' | 'preprint' | 'other';
  citations?: number;
  impact?: 'high' | 'medium' | 'low';
}

export interface Conference {
  name: string;
  title: string;
  year: number;
  location?: string;
  type: 'presentation' | 'poster' | 'keynote' | 'workshop' | 'other';
  international: boolean;
}

export interface Grant {
  title: string;
  agency: string;
  amount?: string;
  year: number;
  role: 'PI' | 'Co-PI' | 'Collaborator';
}

export interface ResearchProject {
  title: string;
  description: string;
  duration: string;
  role: string;
  technologies?: string[];
}

// Enhanced skill database with categories
const SKILLS_DATABASE = {
  programming: ['JavaScript', 'TypeScript', 'Python', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'Swift', 'Kotlin', 'Scala', 'R', 'MATLAB'],
  frontend: ['React', 'Vue.js', 'Angular', 'HTML', 'CSS', 'SASS', 'LESS', 'Bootstrap', 'Tailwind CSS', 'jQuery', 'Next.js', 'Nuxt.js'],
  backend: ['Node.js', 'Express.js', 'Django', 'Flask', 'FastAPI', 'Spring Boot', 'Laravel', 'Ruby on Rails', 'ASP.NET', '.NET Core'],
  databases: ['MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'SQLite', 'Oracle', 'SQL Server', 'Cassandra', 'DynamoDB', 'Firebase'],
  cloud: ['AWS', 'Azure', 'Google Cloud Platform', 'GCP', 'Heroku', 'Vercel', 'Netlify', 'DigitalOcean'],
  devops: ['Docker', 'Kubernetes', 'Jenkins', 'GitLab CI', 'GitHub Actions', 'Terraform', 'Ansible', 'Chef', 'Puppet'],
  tools: ['Git', 'GitHub', 'GitLab', 'Bitbucket', 'Jira', 'Confluence', 'Slack', 'Trello', 'Asana'],
  analytics: ['Excel', 'Pandas', 'NumPy', 'Tableau', 'Power BI', 'D3.js', 'Matplotlib', 'Seaborn', 'Apache Spark'],
  mobile: ['React Native', 'Flutter', 'iOS', 'Android', 'Xamarin', 'Ionic', 'Cordova'],
  testing: ['Jest', 'Mocha', 'Cypress', 'Selenium', 'Pytest', 'JUnit', 'TestNG'],
  others: ['Machine Learning', 'Deep Learning', 'AI', 'Data Science', 'Blockchain', 'IoT', 'AR/VR', 'Microservices', 'GraphQL', 'REST API']
};

const ALL_SKILLS = Object.values(SKILLS_DATABASE).flat();

export async function extractMetadata(file: File): Promise<ExtractedData> {
  try {
    const text = await extractText(file);
    
    // Parallel extraction for better performance
    const [
      name,
      email,
      phone,
      skills,
      education,
      experience,
      summary,
      certifications,
      projects,
      publications,
      conferences,
      researchInterests,
      awards,
      grants,
      teachingExperience,
      supervision,
      patents,
      editorialRoles,
      professionalMemberships,
      languages,
      researchProjects,
      thesisTitle,
      advisor,
      institution,
      graduationYear
    ] = await Promise.all([
      Promise.resolve(extractName(text)),
      Promise.resolve(extractEmail(text)),
      Promise.resolve(extractPhone(text)),
      Promise.resolve(extractSkills(text)),
      Promise.resolve(extractEducation(text)),
      Promise.resolve(extractExperience(text)),
      Promise.resolve(extractSummary(text)),
      Promise.resolve(extractCertifications(text)),
      Promise.resolve(extractProjects(text)),
      Promise.resolve(extractPublications(text)),
      Promise.resolve(extractConferences(text)),
      Promise.resolve(extractResearchInterests(text)),
      Promise.resolve(extractAwards(text)),
      Promise.resolve(extractGrants(text)),
      Promise.resolve(extractTeachingExperience(text)),
      Promise.resolve(extractSupervision(text)),
      Promise.resolve(extractPatents(text)),
      Promise.resolve(extractEditorialRoles(text)),
      Promise.resolve(extractProfessionalMemberships(text)),
      Promise.resolve(extractLanguages(text)),
      Promise.resolve(extractResearchProjects(text)),
      Promise.resolve(extractThesisTitle(text)),
      Promise.resolve(extractAdvisor(text)),
      Promise.resolve(extractInstitution(text)),
      Promise.resolve(extractGraduationYear(text))
    ]);

    // Calculate academic metrics
    const hIndex = calculateHIndex(publications);
    const citationCount = calculateCitationCount(publications);
    const totalScore = calculateTotalScore({
      publications,
      conferences,
      awards,
      grants,
      hIndex,
      citationCount,
      teachingExperience,
      supervision
    });

    return {
      name,
      email,
      phone,
      skills,
      education,
      experience,
      summary,
      certifications,
      projects,
      publications,
      conferences,
      researchInterests,
      awards,
      grants,
      teachingExperience,
      supervision,
      patents,
      editorialRoles,
      professionalMemberships,
      languages,
      researchProjects,
      thesisTitle,
      advisor,
      institution,
      graduationYear,
      hIndex,
      citationCount,
      totalScore
    };
  } catch (error) {
    console.error('Error extracting metadata from file:', file.name, error);
    return {
      name: 'N/A',
      email: 'N/A',
      phone: 'N/A',
      skills: [],
      education: [],
      experience: [],
      summary: '',
      certifications: [],
      projects: [],
      publications: [],
      conferences: [],
      researchInterests: [],
      awards: [],
      grants: [],
      teachingExperience: [],
      supervision: [],
      patents: [],
      editorialRoles: [],
      professionalMemberships: [],
      languages: [],
      researchProjects: [],
      thesisTitle: undefined,
      advisor: undefined,
      institution: undefined,
      graduationYear: undefined,
      hIndex: 0,
      citationCount: 0,
      totalScore: 0
    };
  }
}

async function extractText(file: File): Promise<string> {
  const extension = file.name.split('.').pop()?.toLowerCase();

  try {
    if (extension === 'pdf') {
      return await extractPDFText(file);
    } else if (extension === 'docx') {
      return await extractDocxText(file);
    }
  } catch (error) {
    console.error('Error extracting text from file:', error);
  }
  
  return '';
}

async function extractPDFText(file: File): Promise<string> {
  const arrayBuffer = await file.arrayBuffer();
  const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
  
  const textPromises = [];
  for (let i = 1; i <= pdf.numPages; i++) {
    textPromises.push(
      pdf.getPage(i).then(async (page) => {
        const textContent = await page.getTextContent();
        return textContent.items
          .map((item: any) => item.str)
          .join(' ')
          .replace(/\s+/g, ' ');
      })
    );
  }
  
  const pages = await Promise.all(textPromises);
  return pages.join('\n').trim();
}

async function extractDocxText(file: File): Promise<string> {
  const buffer = await file.arrayBuffer();
  const result = await mammoth.extractRawText({ arrayBuffer: buffer });
  return result.value.replace(/\s+/g, ' ').trim();
}

function extractName(text: string): string {
  const lines = text.split('\n').map(line => line.trim()).filter(Boolean);
  
  // Try first few lines and look for name patterns
  for (let i = 0; i < Math.min(5, lines.length); i++) {
    const line = lines[i];
    
    // Skip common headers/titles
    if (/^(resume|curriculum vitae|cv|profile|contact|address)$/i.test(line)) continue;
    
    // Look for name pattern (2-4 words, starting with capital letters)
    const nameMatch = line.match(/^([A-Z][a-z]+(?:\s+[A-Z][a-z]*){1,3})$/);
    if (nameMatch && nameMatch[1].length > 3 && nameMatch[1].length < 50) {
      return nameMatch[1];
    }
  }
  
  return lines[0]?.substring(0, 50) || 'N/A';
}

function extractEmail(text: string): string {
  const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
  const matches = text.match(emailRegex);
  
  if (matches && matches.length > 0) {
    // Return the first valid email, preferring common domains
    const commonDomains = ['gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com'];
    const preferredMatch = matches.find(email => 
      commonDomains.some(domain => email.toLowerCase().includes(domain))
    );
    return preferredMatch || matches[0];
  }
  
  return 'N/A';
}

function extractPhone(text: string): string {
  const phoneRegex = /(?:\+?1[-.\s]?)?(?:\(?[0-9]{3}\)?[-.\s]?)?[0-9]{3}[-.\s]?[0-9]{4}/g;
  const matches = text.match(phoneRegex);
  
  if (matches && matches.length > 0) {
    // Clean and format the phone number
    const cleaned = matches[0].replace(/[^\d+]/g, '');
    if (cleaned.length >= 10) {
      return matches[0];
    }
  }
  
  return 'N/A';
}

function extractSkills(text: string): string[] {
  const lowerText = text.toLowerCase();
  const foundSkills = new Set<string>();
  
  // Direct skill matching with word boundaries
  ALL_SKILLS.forEach(skill => {
    const skillLower = skill.toLowerCase();
    const regex = new RegExp(`\\b${skillLower.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
    if (regex.test(lowerText)) {
      foundSkills.add(skill);
    }
  });
  
  // Look for skills in dedicated sections
  const skillsSections = extractSkillsSections(text);
  skillsSections.forEach(section => {
    const sectionLower = section.toLowerCase();
    ALL_SKILLS.forEach(skill => {
      const skillLower = skill.toLowerCase();
      if (sectionLower.includes(skillLower)) {
        foundSkills.add(skill);
      }
    });
  });
  
  return Array.from(foundSkills).sort();
}

function extractSkillsSections(text: string): string[] {
  const sections = [];
  const skillsHeaders = /(?:technical\s+skills?|skills?|core\s+competencies|technologies|expertise|proficiencies):/gi;
  
  let match;
  while ((match = skillsHeaders.exec(text)) !== null) {
    const startIndex = match.index;
    const nextSectionIndex = text.indexOf('\n\n', startIndex + 100);
    const sectionText = text.substring(startIndex, nextSectionIndex > 0 ? nextSectionIndex : startIndex + 300);
    sections.push(sectionText);
  }
  
  return sections;
}

function extractEducation(text: string): string[] {
  const educationPatterns = [
    /(?:Bachelor|Master|PhD|Ph\.?D|B\.?[ASE]\.?|M\.?[ASE]\.?|MBA|MS|BS|BA|MA)[\s\w.,()-]*(?:in|of)?\s*[\w\s,.-]+(?:\d{4}|\d{2})/gi,
    /(?:University|College|Institute|School)[\s\w.,()-]*(?:\d{4}|\d{2})/gi,
    /(?:B\.Tech|M\.Tech|B\.Sc|M\.Sc|B\.Com|M\.Com)[\s\w.,()-]*(?:\d{4}|\d{2})/gi
  ];
  
  const education = new Set<string>();
  
  educationPatterns.forEach(pattern => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach(match => {
        const cleaned = match.trim().replace(/\s+/g, ' ');
        if (cleaned.length > 10 && cleaned.length < 150) {
          education.add(cleaned);
        }
      });
    }
  });
  
  return Array.from(education);
}

function extractExperience(text: string): string[] {
  const experiencePatterns = [
    /(?:Software Engineer|Developer|Programmer|Analyst|Manager|Director|Lead|Senior|Junior)\s+at\s+[\w\s.,&-]+(?:\d{4}|\d{2})/gi,
    /(?:worked|employed|served)\s+(?:at|with|for)\s+[\w\s.,&-]+(?:\d{4}|\d{2})/gi,
    /[\w\s.,&-]+(?:Inc|LLC|Corp|Company|Ltd|Co\.)\s*(?:\d{4}|\d{2})/gi
  ];
  
  const experience = new Set<string>();
  
  experiencePatterns.forEach(pattern => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach(match => {
        const cleaned = match.trim().replace(/\s+/g, ' ');
        if (cleaned.length > 10 && cleaned.length < 150) {
          experience.add(cleaned);
        }
      });
    }
  });
  
  return Array.from(experience);
}

function extractSummary(text: string): string {
  const summaryPatterns = [
    /(?:summary|profile|objective|about|introduction):\s*([\s\S]*?)(?:\n\s*\n|$)/gi,
    /(?:professional\s+summary|career\s+objective|personal\s+statement):\s*([\s\S]*?)(?:\n\s*\n|$)/gi
  ];
  
  for (const pattern of summaryPatterns) {
    const match = pattern.exec(text);
    if (match && match[1]) {
      const summary = match[1].trim().replace(/\s+/g, ' ');
      if (summary.length > 20 && summary.length < 500) {
        return summary;
      }
    }
  }
  
  // Fallback: use first paragraph if it looks like a summary
  const paragraphs = text.split('\n\n');
  for (const paragraph of paragraphs.slice(1, 4)) {
    if (paragraph.length > 50 && paragraph.length < 300 && 
        /\b(?:experienced|passionate|dedicated|skilled|professional)\b/i.test(paragraph)) {
      return paragraph.trim().replace(/\s+/g, ' ');
    }
  }
  
  return '';
}

function extractCertifications(text: string): string[] {
  const certificationPatterns = [
    /(?:certified|certification|certificate)[\s\w.,()-]*(?:\d{4}|\d{2})/gi,
    /(?:AWS|Azure|Google Cloud|Microsoft|Oracle|Cisco|CompTIA)[\s\w.,()-]*(?:certified|certification|certificate)/gi,
    /(?:PMP|CISSP|CISM|CEH|CCNA|CCNP|CCIE)/gi
  ];
  
  const certifications = new Set<string>();
  
  certificationPatterns.forEach(pattern => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach(match => {
        const cleaned = match.trim().replace(/\s+/g, ' ');
        if (cleaned.length > 5 && cleaned.length < 100) {
          certifications.add(cleaned);
        }
      });
    }
  });
  
  return Array.from(certifications);
}

function extractProjects(text: string): string[] {
  const projectPatterns = [
    /(?:project|built|developed|created|designed)\s+[\w\s.,()-]+(?:using|with|in)\s+[\w\s.,()-]+/gi,
    /(?:projects?):\s*([\s\S]*?)(?:\n\s*\n|$)/gi
  ];

  const projects = new Set<string>();

  projectPatterns.forEach(pattern => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach(match => {
        const cleaned = match.trim().replace(/\s+/g, ' ');
        if (cleaned.length > 20 && cleaned.length < 200) {
          projects.add(cleaned);
        }
      });
    }
  });

  return Array.from(projects);
}

// PhD Scholar specific extraction functions
function extractPublications(text: string): Publication[] {
  const publications: Publication[] = [];
  const publicationPatterns = [
    /(?:publications?|papers?|articles?)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:journal|conference|proceedings)[^\n]*/gi,
    /\d{4}[^\n]*(?:journal|conference|proceedings|published)[^\n]*/gi
  ];

  for (const pattern of publicationPatterns) {
    const matches = text.match(pattern);
    if (matches) {
      const pubText = Array.isArray(matches) ? matches.join('\n') : matches;
      const lines = pubText.split(/\n|•|\-/)
        .map(p => p.trim())
        .filter(p => p && p.length > 20);

      for (const line of lines) {
        const yearMatch = line.match(/\b(19|20)\d{2}\b/);
        const year = yearMatch ? parseInt(yearMatch[0]) : new Date().getFullYear();

        let type: Publication['type'] = 'other';
        if (line.toLowerCase().includes('journal')) type = 'journal';
        else if (line.toLowerCase().includes('conference')) type = 'conference';
        else if (line.toLowerCase().includes('book')) type = 'book';

        publications.push({
          title: line.replace(/^\d+\.\s*/, '').trim(),
          authors: extractAuthorsFromPublication(line),
          journal: extractJournalFromPublication(line),
          year,
          type,
          citations: extractCitationsFromPublication(line),
          impact: determinePublicationImpact(line)
        });
      }
    }
  }
  return publications;
}

function extractConferences(text: string): Conference[] {
  const conferences: Conference[] = [];
  const conferencePatterns = [
    /(?:conferences?|presentations?|talks?)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:presented|spoke|keynote)[^\n]*(?:conference|symposium|workshop)[^\n]*/gi
  ];

  for (const pattern of conferencePatterns) {
    const matches = text.match(pattern);
    if (matches) {
      const confText = Array.isArray(matches) ? matches.join('\n') : matches;
      const lines = confText.split(/\n|•|\-/)
        .map(c => c.trim())
        .filter(c => c && c.length > 15);

      for (const line of lines) {
        const yearMatch = line.match(/\b(19|20)\d{2}\b/);
        const year = yearMatch ? parseInt(yearMatch[0]) : new Date().getFullYear();

        let type: Conference['type'] = 'presentation';
        if (line.toLowerCase().includes('poster')) type = 'poster';
        else if (line.toLowerCase().includes('keynote')) type = 'keynote';
        else if (line.toLowerCase().includes('workshop')) type = 'workshop';

        conferences.push({
          name: extractConferenceName(line),
          title: line.replace(/^\d+\.\s*/, '').trim(),
          year,
          location: extractLocationFromConference(line),
          type,
          international: line.toLowerCase().includes('international') ||
                        line.toLowerCase().includes('global') ||
                        line.toLowerCase().includes('world')
        });
      }
    }
  }
  return conferences;
}

function extractResearchInterests(text: string): string[] {
  const patterns = [
    /(?:research interests?|research areas?|specialization)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:expertise|focus)[:\-]?\s*([^\n]+)/i
  ];

  let interests: string[] = [];
  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match) {
      const interestsText = match[1];
      interests = [...interests, ...interestsText.split(/[,;|•\-\n]/)
        .map(i => i.trim())
        .filter(i => i && i.length > 3)];
    }
  }
  return [...new Set(interests)];
}

function extractAwards(text: string): string[] {
  const patterns = [
    /(?:awards?|honors?|recognitions?|achievements?)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:winner|recipient|awarded)[^\n]*/gi
  ];

  let awards: string[] = [];
  for (const pattern of patterns) {
    const matches = text.match(pattern);
    if (matches) {
      const awardsText = Array.isArray(matches) ? matches.join('\n') : matches;
      awards = [...awards, ...awardsText.split(/\n|•|\-/)
        .map(a => a.trim())
        .filter(a => a && a.length > 5)
        .map(a => a.replace(/^[•\-\*]\s*/, ''))];
    }
  }
  return [...new Set(awards)];
}

function extractGrants(text: string): Grant[] {
  const grants: Grant[] = [];
  const grantPatterns = [
    /(?:grants?|funding|sponsored)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:NSF|NIH|DOE|NASA|DARPA)[^\n]*/gi
  ];

  for (const pattern of grantPatterns) {
    const matches = text.match(pattern);
    if (matches) {
      const grantText = Array.isArray(matches) ? matches.join('\n') : matches;
      const lines = grantText.split(/\n|•|\-/)
        .map(g => g.trim())
        .filter(g => g && g.length > 10);

      for (const line of lines) {
        const yearMatch = line.match(/\b(19|20)\d{2}\b/);
        const year = yearMatch ? parseInt(yearMatch[0]) : new Date().getFullYear();

        let role: Grant['role'] = 'Collaborator';
        if (line.toLowerCase().includes('pi') || line.toLowerCase().includes('principal')) role = 'PI';
        else if (line.toLowerCase().includes('co-pi')) role = 'Co-PI';

        grants.push({
          title: line.replace(/^\d+\.\s*/, '').trim(),
          agency: extractAgencyFromGrant(line),
          amount: extractAmountFromGrant(line),
          year,
          role
        });
      }
    }
  }
  return grants;
}

function extractTeachingExperience(text: string): string[] {
  const patterns = [
    /(?:teaching|instructor|professor|lecturer)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:taught|course|class)[^\n]*/gi
  ];

  let teaching: string[] = [];
  for (const pattern of patterns) {
    const matches = text.match(pattern);
    if (matches) {
      const teachingText = Array.isArray(matches) ? matches.join('\n') : matches;
      teaching = [...teaching, ...teachingText.split(/\n|•|\-/)
        .map(t => t.trim())
        .filter(t => t && t.length > 10)
        .map(t => t.replace(/^[•\-\*]\s*/, ''))];
    }
  }
  return [...new Set(teaching)];
}

function extractSupervision(text: string): string[] {
  const patterns = [
    /(?:supervision|supervised|mentored|advisor)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:phd|master|undergraduate)\s+(?:student|thesis)[^\n]*/gi
  ];

  let supervision: string[] = [];
  for (const pattern of patterns) {
    const matches = text.match(pattern);
    if (matches) {
      const supervisionText = Array.isArray(matches) ? matches.join('\n') : matches;
      supervision = [...supervision, ...supervisionText.split(/\n|•|\-/)
        .map(s => s.trim())
        .filter(s => s && s.length > 5)
        .map(s => s.replace(/^[•\-\*]\s*/, ''))];
    }
  }
  return [...new Set(supervision)];
}

function extractPatents(text: string): string[] {
  const patterns = [
    /(?:patents?|intellectual property)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:patent|US\s*\d+)[^\n]*/gi
  ];

  let patents: string[] = [];
  for (const pattern of patterns) {
    const matches = text.match(pattern);
    if (matches) {
      const patentsText = Array.isArray(matches) ? matches.join('\n') : matches;
      patents = [...patents, ...patentsText.split(/\n|•|\-/)
        .map(p => p.trim())
        .filter(p => p && p.length > 10)
        .map(p => p.replace(/^[•\-\*]\s*/, ''))];
    }
  }
  return [...new Set(patents)];
}

function extractEditorialRoles(text: string): string[] {
  const patterns = [
    /(?:editorial|editor|reviewer|board)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:journal|conference)\s+(?:editor|reviewer)[^\n]*/gi
  ];

  let roles: string[] = [];
  for (const pattern of patterns) {
    const matches = text.match(pattern);
    if (matches) {
      const rolesText = Array.isArray(matches) ? matches.join('\n') : matches;
      roles = [...roles, ...rolesText.split(/\n|•|\-/)
        .map(r => r.trim())
        .filter(r => r && r.length > 5)
        .map(r => r.replace(/^[•\-\*]\s*/, ''))];
    }
  }
  return [...new Set(roles)];
}

function extractProfessionalMemberships(text: string): string[] {
  const patterns = [
    /(?:memberships?|member|society|association)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:IEEE|ACM|AAAI|SIGKDD)[^\n]*/gi
  ];

  let memberships: string[] = [];
  for (const pattern of patterns) {
    const matches = text.match(pattern);
    if (matches) {
      const membershipsText = Array.isArray(matches) ? matches.join('\n') : matches;
      memberships = [...memberships, ...membershipsText.split(/\n|•|\-/)
        .map(m => m.trim())
        .filter(m => m && m.length > 3)
        .map(m => m.replace(/^[•\-\*]\s*/, ''))];
    }
  }
  return [...new Set(memberships)];
}

function extractLanguages(text: string): string[] {
  const patterns = [
    /(?:languages?|linguistic)[:\-]?\s*([^\n]+)/i,
    /(?:fluent|native|proficient)\s+(?:in\s+)?([A-Za-z]+)/gi
  ];

  let languages: string[] = [];
  for (const pattern of patterns) {
    const matches = text.match(pattern);
    if (matches) {
      const languagesText = Array.isArray(matches) ? matches.join('\n') : matches;
      languages = [...languages, ...languagesText.split(/[,;|•\-\n]/)
        .map(l => l.trim())
        .filter(l => l && l.length > 2 && l.length < 20)];
    }
  }
  return [...new Set(languages)];
}