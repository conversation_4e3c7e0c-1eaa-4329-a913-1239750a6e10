import * as pdfjsLib from 'pdfjs-dist';
import mammoth from 'mammoth';

// Configure PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/4.0.379/pdf.worker.min.js`;

export interface ExtractedData {
  name: string;
  email: string;
  phone: string;
  skills: string[];
  education: string[];
  experience: string[];
  summary: string;
  certifications: string[];
  projects: string[];
}

// Enhanced skill database with categories
const SKILLS_DATABASE = {
  programming: ['JavaScript', 'TypeScript', 'Python', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', '<PERSON>', '<PERSON>tlin', '<PERSON>ala', 'R', 'MATLAB'],
  frontend: ['React', 'Vue.js', 'Angular', 'HTML', 'CSS', 'SASS', 'LESS', 'Bootstrap', 'Tailwind CSS', 'jQuery', 'Next.js', 'Nuxt.js'],
  backend: ['Node.js', 'Express.js', 'Django', 'Flask', 'FastAPI', 'Spring Boot', 'Laravel', 'Ruby on Rails', 'ASP.NET', '.NET Core'],
  databases: ['MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'SQLite', 'Oracle', 'SQL Server', 'Cassandra', 'DynamoDB', 'Firebase'],
  cloud: ['AWS', 'Azure', 'Google Cloud Platform', 'GCP', 'Heroku', 'Vercel', 'Netlify', 'DigitalOcean'],
  devops: ['Docker', 'Kubernetes', 'Jenkins', 'GitLab CI', 'GitHub Actions', 'Terraform', 'Ansible', 'Chef', 'Puppet'],
  tools: ['Git', 'GitHub', 'GitLab', 'Bitbucket', 'Jira', 'Confluence', 'Slack', 'Trello', 'Asana'],
  analytics: ['Excel', 'Pandas', 'NumPy', 'Tableau', 'Power BI', 'D3.js', 'Matplotlib', 'Seaborn', 'Apache Spark'],
  mobile: ['React Native', 'Flutter', 'iOS', 'Android', 'Xamarin', 'Ionic', 'Cordova'],
  testing: ['Jest', 'Mocha', 'Cypress', 'Selenium', 'Pytest', 'JUnit', 'TestNG'],
  others: ['Machine Learning', 'Deep Learning', 'AI', 'Data Science', 'Blockchain', 'IoT', 'AR/VR', 'Microservices', 'GraphQL', 'REST API']
};

const ALL_SKILLS = Object.values(SKILLS_DATABASE).flat();

export async function extractMetadata(file: File): Promise<ExtractedData> {
  try {
    const text = await extractText(file);
    
    // Parallel extraction for better performance
    const [
      name,
      email,
      phone,
      skills,
      education,
      experience,
      summary,
      certifications,
      projects
    ] = await Promise.all([
      Promise.resolve(extractName(text)),
      Promise.resolve(extractEmail(text)),
      Promise.resolve(extractPhone(text)),
      Promise.resolve(extractSkills(text)),
      Promise.resolve(extractEducation(text)),
      Promise.resolve(extractExperience(text)),
      Promise.resolve(extractSummary(text)),
      Promise.resolve(extractCertifications(text)),
      Promise.resolve(extractProjects(text))
    ]);

    return {
      name,
      email,
      phone,
      skills,
      education,
      experience,
      summary,
      certifications,
      projects
    };
  } catch (error) {
    console.error('Error extracting metadata from file:', file.name, error);
    return {
      name: 'N/A',
      email: 'N/A',
      phone: 'N/A',
      skills: [],
      education: [],
      experience: [],
      summary: '',
      certifications: [],
      projects: []
    };
  }
}

async function extractText(file: File): Promise<string> {
  const extension = file.name.split('.').pop()?.toLowerCase();

  try {
    if (extension === 'pdf') {
      return await extractPDFText(file);
    } else if (extension === 'docx') {
      return await extractDocxText(file);
    }
  } catch (error) {
    console.error('Error extracting text from file:', error);
  }
  
  return '';
}

async function extractPDFText(file: File): Promise<string> {
  const arrayBuffer = await file.arrayBuffer();
  const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
  
  const textPromises = [];
  for (let i = 1; i <= pdf.numPages; i++) {
    textPromises.push(
      pdf.getPage(i).then(async (page) => {
        const textContent = await page.getTextContent();
        return textContent.items
          .map((item: any) => item.str)
          .join(' ')
          .replace(/\s+/g, ' ');
      })
    );
  }
  
  const pages = await Promise.all(textPromises);
  return pages.join('\n').trim();
}

async function extractDocxText(file: File): Promise<string> {
  const buffer = await file.arrayBuffer();
  const result = await mammoth.extractRawText({ arrayBuffer: buffer });
  return result.value.replace(/\s+/g, ' ').trim();
}

function extractName(text: string): string {
  const lines = text.split('\n').map(line => line.trim()).filter(Boolean);
  
  // Try first few lines and look for name patterns
  for (let i = 0; i < Math.min(5, lines.length); i++) {
    const line = lines[i];
    
    // Skip common headers/titles
    if (/^(resume|curriculum vitae|cv|profile|contact|address)$/i.test(line)) continue;
    
    // Look for name pattern (2-4 words, starting with capital letters)
    const nameMatch = line.match(/^([A-Z][a-z]+(?:\s+[A-Z][a-z]*){1,3})$/);
    if (nameMatch && nameMatch[1].length > 3 && nameMatch[1].length < 50) {
      return nameMatch[1];
    }
  }
  
  return lines[0]?.substring(0, 50) || 'N/A';
}

function extractEmail(text: string): string {
  const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
  const matches = text.match(emailRegex);
  
  if (matches && matches.length > 0) {
    // Return the first valid email, preferring common domains
    const commonDomains = ['gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com'];
    const preferredMatch = matches.find(email => 
      commonDomains.some(domain => email.toLowerCase().includes(domain))
    );
    return preferredMatch || matches[0];
  }
  
  return 'N/A';
}

function extractPhone(text: string): string {
  const phoneRegex = /(?:\+?1[-.\s]?)?(?:\(?[0-9]{3}\)?[-.\s]?)?[0-9]{3}[-.\s]?[0-9]{4}/g;
  const matches = text.match(phoneRegex);
  
  if (matches && matches.length > 0) {
    // Clean and format the phone number
    const cleaned = matches[0].replace(/[^\d+]/g, '');
    if (cleaned.length >= 10) {
      return matches[0];
    }
  }
  
  return 'N/A';
}

function extractSkills(text: string): string[] {
  const lowerText = text.toLowerCase();
  const foundSkills = new Set<string>();
  
  // Direct skill matching with word boundaries
  ALL_SKILLS.forEach(skill => {
    const skillLower = skill.toLowerCase();
    const regex = new RegExp(`\\b${skillLower.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
    if (regex.test(lowerText)) {
      foundSkills.add(skill);
    }
  });
  
  // Look for skills in dedicated sections
  const skillsSections = extractSkillsSections(text);
  skillsSections.forEach(section => {
    const sectionLower = section.toLowerCase();
    ALL_SKILLS.forEach(skill => {
      const skillLower = skill.toLowerCase();
      if (sectionLower.includes(skillLower)) {
        foundSkills.add(skill);
      }
    });
  });
  
  return Array.from(foundSkills).sort();
}

function extractSkillsSections(text: string): string[] {
  const sections = [];
  const skillsHeaders = /(?:technical\s+skills?|skills?|core\s+competencies|technologies|expertise|proficiencies):/gi;
  
  let match;
  while ((match = skillsHeaders.exec(text)) !== null) {
    const startIndex = match.index;
    const nextSectionIndex = text.indexOf('\n\n', startIndex + 100);
    const sectionText = text.substring(startIndex, nextSectionIndex > 0 ? nextSectionIndex : startIndex + 300);
    sections.push(sectionText);
  }
  
  return sections;
}

function extractEducation(text: string): string[] {
  const educationPatterns = [
    /(?:Bachelor|Master|PhD|Ph\.?D|B\.?[ASE]\.?|M\.?[ASE]\.?|MBA|MS|BS|BA|MA)[\s\w.,()-]*(?:in|of)?\s*[\w\s,.-]+(?:\d{4}|\d{2})/gi,
    /(?:University|College|Institute|School)[\s\w.,()-]*(?:\d{4}|\d{2})/gi,
    /(?:B\.Tech|M\.Tech|B\.Sc|M\.Sc|B\.Com|M\.Com)[\s\w.,()-]*(?:\d{4}|\d{2})/gi
  ];
  
  const education = new Set<string>();
  
  educationPatterns.forEach(pattern => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach(match => {
        const cleaned = match.trim().replace(/\s+/g, ' ');
        if (cleaned.length > 10 && cleaned.length < 150) {
          education.add(cleaned);
        }
      });
    }
  });
  
  return Array.from(education);
}

function extractExperience(text: string): string[] {
  const experiencePatterns = [
    /(?:Software Engineer|Developer|Programmer|Analyst|Manager|Director|Lead|Senior|Junior)\s+at\s+[\w\s.,&-]+(?:\d{4}|\d{2})/gi,
    /(?:worked|employed|served)\s+(?:at|with|for)\s+[\w\s.,&-]+(?:\d{4}|\d{2})/gi,
    /[\w\s.,&-]+(?:Inc|LLC|Corp|Company|Ltd|Co\.)\s*(?:\d{4}|\d{2})/gi
  ];
  
  const experience = new Set<string>();
  
  experiencePatterns.forEach(pattern => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach(match => {
        const cleaned = match.trim().replace(/\s+/g, ' ');
        if (cleaned.length > 10 && cleaned.length < 150) {
          experience.add(cleaned);
        }
      });
    }
  });
  
  return Array.from(experience);
}

function extractSummary(text: string): string {
  const summaryPatterns = [
    /(?:summary|profile|objective|about|introduction):\s*([\s\S]*?)(?:\n\s*\n|$)/gi,
    /(?:professional\s+summary|career\s+objective|personal\s+statement):\s*([\s\S]*?)(?:\n\s*\n|$)/gi
  ];
  
  for (const pattern of summaryPatterns) {
    const match = pattern.exec(text);
    if (match && match[1]) {
      const summary = match[1].trim().replace(/\s+/g, ' ');
      if (summary.length > 20 && summary.length < 500) {
        return summary;
      }
    }
  }
  
  // Fallback: use first paragraph if it looks like a summary
  const paragraphs = text.split('\n\n');
  for (const paragraph of paragraphs.slice(1, 4)) {
    if (paragraph.length > 50 && paragraph.length < 300 && 
        /\b(?:experienced|passionate|dedicated|skilled|professional)\b/i.test(paragraph)) {
      return paragraph.trim().replace(/\s+/g, ' ');
    }
  }
  
  return '';
}

function extractCertifications(text: string): string[] {
  const certificationPatterns = [
    /(?:certified|certification|certificate)[\s\w.,()-]*(?:\d{4}|\d{2})/gi,
    /(?:AWS|Azure|Google Cloud|Microsoft|Oracle|Cisco|CompTIA)[\s\w.,()-]*(?:certified|certification|certificate)/gi,
    /(?:PMP|CISSP|CISM|CEH|CCNA|CCNP|CCIE)/gi
  ];
  
  const certifications = new Set<string>();
  
  certificationPatterns.forEach(pattern => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach(match => {
        const cleaned = match.trim().replace(/\s+/g, ' ');
        if (cleaned.length > 5 && cleaned.length < 100) {
          certifications.add(cleaned);
        }
      });
    }
  });
  
  return Array.from(certifications);
}

function extractProjects(text: string): string[] {
  const projectPatterns = [
    /(?:project|built|developed|created|designed)\s+[\w\s.,()-]+(?:using|with|in)\s+[\w\s.,()-]+/gi,
    /(?:projects?):\s*([\s\S]*?)(?:\n\s*\n|$)/gi
  ];
  
  const projects = new Set<string>();
  
  projectPatterns.forEach(pattern => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach(match => {
        const cleaned = match.trim().replace(/\s+/g, ' ');
        if (cleaned.length > 20 && cleaned.length < 200) {
          projects.add(cleaned);
        }
      });
    }
  });
  
  return Array.from(projects);
}