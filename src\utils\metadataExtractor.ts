import * as pdfjsLib from 'pdfjs-dist';
import mammoth from 'mammoth';

// Configure PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/4.0.379/pdf.worker.min.js`;

export interface ExtractedData {
  name: string;
  email: string;
  phone: string;
  skills: string[];
  education: string[];
  experience: string[];
  summary: string;
  certifications: string[];
  projects: string[];
  // PhD Scholar specific metadata
  publications: Publication[];
  conferences: Conference[];
  researchInterests: string[];
  awards: string[];
  grants: Grant[];
  teachingExperience: string[];
  supervision: string[];
  patents: string[];
  editorialRoles: string[];
  professionalMemberships: string[];
  languages: string[];
  researchProjects: ResearchProject[];
  thesisTitle?: string;
  advisor?: string;
  institution?: string;
  graduationYear?: number;
  hIndex?: number;
  citationCount?: number;
  totalScore?: number; // Overall scoring for ranking
}

export interface Publication {
  title: string;
  authors: string[];
  journal: string;
  year: number;
  type: 'journal' | 'conference' | 'book' | 'chapter' | 'preprint' | 'other';
  citations?: number;
  impact?: 'high' | 'medium' | 'low';
}

export interface Conference {
  name: string;
  title: string;
  year: number;
  location?: string;
  type: 'presentation' | 'poster' | 'keynote' | 'workshop' | 'other';
  international: boolean;
}

export interface Grant {
  title: string;
  agency: string;
  amount?: string;
  year: number;
  role: 'PI' | 'Co-PI' | 'Collaborator';
}

export interface ResearchProject {
  title: string;
  description: string;
  duration: string;
  role: string;
  technologies?: string[];
}

// Enhanced skill database with categories
const SKILLS_DATABASE = {
  programming: ['JavaScript', 'TypeScript', 'Python', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'Swift', 'Kotlin', 'Scala', 'R', 'MATLAB'],
  frontend: ['React', 'Vue.js', 'Angular', 'HTML', 'CSS', 'SASS', 'LESS', 'Bootstrap', 'Tailwind CSS', 'jQuery', 'Next.js', 'Nuxt.js'],
  backend: ['Node.js', 'Express.js', 'Django', 'Flask', 'FastAPI', 'Spring Boot', 'Laravel', 'Ruby on Rails', 'ASP.NET', '.NET Core'],
  databases: ['MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'SQLite', 'Oracle', 'SQL Server', 'Cassandra', 'DynamoDB', 'Firebase'],
  cloud: ['AWS', 'Azure', 'Google Cloud Platform', 'GCP', 'Heroku', 'Vercel', 'Netlify', 'DigitalOcean'],
  devops: ['Docker', 'Kubernetes', 'Jenkins', 'GitLab CI', 'GitHub Actions', 'Terraform', 'Ansible', 'Chef', 'Puppet'],
  tools: ['Git', 'GitHub', 'GitLab', 'Bitbucket', 'Jira', 'Confluence', 'Slack', 'Trello', 'Asana'],
  analytics: ['Excel', 'Pandas', 'NumPy', 'Tableau', 'Power BI', 'D3.js', 'Matplotlib', 'Seaborn', 'Apache Spark'],
  mobile: ['React Native', 'Flutter', 'iOS', 'Android', 'Xamarin', 'Ionic', 'Cordova'],
  testing: ['Jest', 'Mocha', 'Cypress', 'Selenium', 'Pytest', 'JUnit', 'TestNG'],
  others: ['Machine Learning', 'Deep Learning', 'AI', 'Data Science', 'Blockchain', 'IoT', 'AR/VR', 'Microservices', 'GraphQL', 'REST API']
};

const ALL_SKILLS = Object.values(SKILLS_DATABASE).flat();

export async function extractMetadata(file: File): Promise<ExtractedData> {
  try {
    const text = await extractText(file);
    
    // Parallel extraction for better performance
    const [
      name,
      email,
      phone,
      skills,
      education,
      experience,
      summary,
      certifications,
      projects,
      publications,
      conferences,
      researchInterests,
      awards,
      grants,
      teachingExperience,
      supervision,
      patents,
      editorialRoles,
      professionalMemberships,
      languages,
      researchProjects,
      thesisTitle,
      advisor,
      institution,
      graduationYear
    ] = await Promise.all([
      Promise.resolve(extractName(text)),
      Promise.resolve(extractEmail(text)),
      Promise.resolve(extractPhone(text)),
      Promise.resolve(extractSkills(text)),
      Promise.resolve(extractEducation(text)),
      Promise.resolve(extractExperience(text)),
      Promise.resolve(extractSummary(text)),
      Promise.resolve(extractCertifications(text)),
      Promise.resolve(extractProjects(text)),
      Promise.resolve(extractPublications(text)),
      Promise.resolve(extractConferences(text)),
      Promise.resolve(extractResearchInterests(text)),
      Promise.resolve(extractAwards(text)),
      Promise.resolve(extractGrants(text)),
      Promise.resolve(extractTeachingExperience(text)),
      Promise.resolve(extractSupervision(text)),
      Promise.resolve(extractPatents(text)),
      Promise.resolve(extractEditorialRoles(text)),
      Promise.resolve(extractProfessionalMemberships(text)),
      Promise.resolve(extractLanguages(text)),
      Promise.resolve(extractResearchProjects(text)),
      Promise.resolve(extractThesisTitle(text)),
      Promise.resolve(extractAdvisor(text)),
      Promise.resolve(extractInstitution(text)),
      Promise.resolve(extractGraduationYear(text))
    ]);

    // Calculate academic metrics
    const hIndex = calculateHIndex(publications);
    const citationCount = calculateCitationCount(publications);
    const totalScore = calculateTotalScore({
      publications,
      conferences,
      awards,
      grants,
      hIndex,
      citationCount,
      teachingExperience,
      supervision
    });

    return {
      name,
      email,
      phone,
      skills,
      education,
      experience,
      summary,
      certifications,
      projects,
      publications,
      conferences,
      researchInterests,
      awards,
      grants,
      teachingExperience,
      supervision,
      patents,
      editorialRoles,
      professionalMemberships,
      languages,
      researchProjects,
      thesisTitle,
      advisor,
      institution,
      graduationYear,
      hIndex,
      citationCount,
      totalScore
    };
  } catch (error) {
    console.error('Error extracting metadata from file:', file.name, error);
    return {
      name: 'N/A',
      email: 'N/A',
      phone: 'N/A',
      skills: [],
      education: [],
      experience: [],
      summary: '',
      certifications: [],
      projects: [],
      publications: [],
      conferences: [],
      researchInterests: [],
      awards: [],
      grants: [],
      teachingExperience: [],
      supervision: [],
      patents: [],
      editorialRoles: [],
      professionalMemberships: [],
      languages: [],
      researchProjects: [],
      thesisTitle: undefined,
      advisor: undefined,
      institution: undefined,
      graduationYear: undefined,
      hIndex: 0,
      citationCount: 0,
      totalScore: 0
    };
  }
}

async function extractText(file: File): Promise<string> {
  const extension = file.name.split('.').pop()?.toLowerCase();

  try {
    if (extension === 'pdf') {
      return await extractPDFText(file);
    } else if (extension === 'docx') {
      return await extractDocxText(file);
    }
  } catch (error) {
    console.error('Error extracting text from file:', error);
  }
  
  return '';
}

async function extractPDFText(file: File): Promise<string> {
  const arrayBuffer = await file.arrayBuffer();
  const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
  
  const textPromises = [];
  for (let i = 1; i <= pdf.numPages; i++) {
    textPromises.push(
      pdf.getPage(i).then(async (page) => {
        const textContent = await page.getTextContent();
        return textContent.items
          .map((item: any) => item.str)
          .join(' ')
          .replace(/\s+/g, ' ');
      })
    );
  }
  
  const pages = await Promise.all(textPromises);
  return pages.join('\n').trim();
}

async function extractDocxText(file: File): Promise<string> {
  const buffer = await file.arrayBuffer();
  const result = await mammoth.extractRawText({ arrayBuffer: buffer });
  return result.value.replace(/\s+/g, ' ').trim();
}

function extractName(text: string): string {
  const lines = text.split('\n').map(line => line.trim()).filter(Boolean);
  
  // Try first few lines and look for name patterns
  for (let i = 0; i < Math.min(5, lines.length); i++) {
    const line = lines[i];
    
    // Skip common headers/titles
    if (/^(resume|curriculum vitae|cv|profile|contact|address)$/i.test(line)) continue;
    
    // Look for name pattern (2-4 words, starting with capital letters)
    const nameMatch = line.match(/^([A-Z][a-z]+(?:\s+[A-Z][a-z]*){1,3})$/);
    if (nameMatch && nameMatch[1].length > 3 && nameMatch[1].length < 50) {
      return nameMatch[1];
    }
  }
  
  return lines[0]?.substring(0, 50) || 'N/A';
}

function extractEmail(text: string): string {
  const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
  const matches = text.match(emailRegex);
  
  if (matches && matches.length > 0) {
    // Return the first valid email, preferring common domains
    const commonDomains = ['gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com'];
    const preferredMatch = matches.find(email => 
      commonDomains.some(domain => email.toLowerCase().includes(domain))
    );
    return preferredMatch || matches[0];
  }
  
  return 'N/A';
}

function extractPhone(text: string): string {
  const phoneRegex = /(?:\+?1[-.\s]?)?(?:\(?[0-9]{3}\)?[-.\s]?)?[0-9]{3}[-.\s]?[0-9]{4}/g;
  const matches = text.match(phoneRegex);
  
  if (matches && matches.length > 0) {
    // Clean and format the phone number
    const cleaned = matches[0].replace(/[^\d+]/g, '');
    if (cleaned.length >= 10) {
      return matches[0];
    }
  }
  
  return 'N/A';
}

function extractSkills(text: string): string[] {
  const lowerText = text.toLowerCase();
  const foundSkills = new Set<string>();
  
  // Direct skill matching with word boundaries
  ALL_SKILLS.forEach(skill => {
    const skillLower = skill.toLowerCase();
    const regex = new RegExp(`\\b${skillLower.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
    if (regex.test(lowerText)) {
      foundSkills.add(skill);
    }
  });
  
  // Look for skills in dedicated sections
  const skillsSections = extractSkillsSections(text);
  skillsSections.forEach(section => {
    const sectionLower = section.toLowerCase();
    ALL_SKILLS.forEach(skill => {
      const skillLower = skill.toLowerCase();
      if (sectionLower.includes(skillLower)) {
        foundSkills.add(skill);
      }
    });
  });
  
  return Array.from(foundSkills).sort();
}

function extractSkillsSections(text: string): string[] {
  const sections = [];
  const skillsHeaders = /(?:technical\s+skills?|skills?|core\s+competencies|technologies|expertise|proficiencies):/gi;
  
  let match;
  while ((match = skillsHeaders.exec(text)) !== null) {
    const startIndex = match.index;
    const nextSectionIndex = text.indexOf('\n\n', startIndex + 100);
    const sectionText = text.substring(startIndex, nextSectionIndex > 0 ? nextSectionIndex : startIndex + 300);
    sections.push(sectionText);
  }
  
  return sections;
}

function extractEducation(text: string): string[] {
  const education = new Set<string>();

  // Look for explicit education sections first
  const educationSectionPatterns = [
    /(?:education|academic\s+background|qualifications)[:\-]?\s*([\s\S]*?)(?:\n\s*(?:[A-Z][A-Z\s]*[:\-]|$))/gi
  ];

  for (const pattern of educationSectionPatterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      const sectionText = match[1];
      // Extract individual education entries from the section
      const entries = sectionText.split(/\n/)
        .map(line => line.trim())
        .filter(line => {
          // Only include lines that look like actual education entries
          return line.length > 15 &&
                 line.length < 200 &&
                 (/(?:Bachelor|Master|PhD|Ph\.?D|B\.?[ASE]\.?|M\.?[ASE]\.?|MBA|MS|BS|BA|MA|University|College|Institute)/i.test(line) ||
                  /\d{4}/.test(line)) &&
                 !line.toLowerCase().startsWith('skills') &&
                 !line.toLowerCase().startsWith('experience') &&
                 !line.toLowerCase().startsWith('projects');
        });

      entries.forEach(entry => {
        if (entry.trim()) {
          education.add(entry.trim());
        }
      });
    }
  }

  // If no education section found, look for specific degree patterns (more conservative)
  if (education.size === 0) {
    const degreePatterns = [
      /(?:Bachelor|Master|PhD|Ph\.?D|B\.?[ASE]\.?|M\.?[ASE]\.?|MBA|MS|BS|BA|MA)[\s\w.,()-]*(?:in|of)\s*[\w\s,.-]+(?:from\s+)?(?:University|College|Institute)[\s\w.,()-]*(?:\d{4}|\d{2})/gi,
      /(?:University|College|Institute)\s+of\s+[\w\s,.-]+[\s\w.,()-]*(?:\d{4}|\d{2})/gi
    ];

    degreePatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const cleaned = match.trim().replace(/\s+/g, ' ');
          // Be more strict about what constitutes valid education
          if (cleaned.length > 20 &&
              cleaned.length < 150 &&
              (/(?:Bachelor|Master|PhD|Ph\.?D|University|College|Institute)/i.test(cleaned))
             ) {
            education.add(cleaned);
          }
        });
      }
    });
  }

  return Array.from(education);
}

function extractExperience(text: string): string[] {
  const experience = new Set<string>();

  // Look for explicit experience sections first
  const experienceSectionPatterns = [
    /(?:work\s+experience|professional\s+experience|employment\s+history|career\s+history)[:\-]?\s*([\s\S]*?)(?:\n\s*(?:[A-Z][A-Z\s]*[:\-]|$))/gi,
    /(?:experience)[:\-]?\s*([\s\S]*?)(?:\n\s*(?:education|skills|projects|[A-Z][A-Z\s]*[:\-])|$)/gi
  ];

  for (const pattern of experienceSectionPatterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      const sectionText = match[1];
      // Extract individual experience entries from the section
      const entries = sectionText.split(/\n/)
        .map(line => line.trim())
        .filter(line => {
          // Only include lines that look like actual job entries
          return line.length > 15 &&
                 line.length < 200 &&
                 (line.includes('at ') || line.includes(' - ') || /\d{4}/.test(line)) &&
                 !line.toLowerCase().startsWith('skills') &&
                 !line.toLowerCase().startsWith('education') &&
                 !line.toLowerCase().startsWith('projects');
        });

      entries.forEach(entry => {
        if (entry.trim()) {
          experience.add(entry.trim());
        }
      });
    }
  }

  // If no experience section found, look for specific job title patterns (more conservative)
  if (experience.size === 0) {
    const jobPatterns = [
      /(?:Software Engineer|Data Scientist|Research Scientist|Professor|Postdoc|Research Associate|Developer|Analyst)\s+(?:at|@)\s+[A-Za-z][^,\n]*(?:\d{4}|\d{2})/gi,
      /(?:worked|employed)\s+(?:as|at)\s+[A-Za-z][^,\n]*(?:\d{4}|\d{2})/gi
    ];

    jobPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const cleaned = match.trim().replace(/\s+/g, ' ');
          // Be more strict about what constitutes valid experience
          if (cleaned.length > 20 &&
              cleaned.length < 150 &&
              /\d{4}/.test(cleaned) &&
              !cleaned.toLowerCase().includes('university') // Avoid education entries
             ) {
            experience.add(cleaned);
          }
        });
      }
    });
  }

  return Array.from(experience);
}

function extractSummary(text: string): string {
  const summaryPatterns = [
    /(?:summary|profile|objective|about|introduction):\s*([\s\S]*?)(?:\n\s*\n|$)/gi,
    /(?:professional\s+summary|career\s+objective|personal\s+statement):\s*([\s\S]*?)(?:\n\s*\n|$)/gi
  ];
  
  for (const pattern of summaryPatterns) {
    const match = pattern.exec(text);
    if (match && match[1]) {
      const summary = match[1].trim().replace(/\s+/g, ' ');
      if (summary.length > 20 && summary.length < 500) {
        return summary;
      }
    }
  }
  
  // Fallback: use first paragraph if it looks like a summary
  const paragraphs = text.split('\n\n');
  for (const paragraph of paragraphs.slice(1, 4)) {
    if (paragraph.length > 50 && paragraph.length < 300 && 
        /\b(?:experienced|passionate|dedicated|skilled|professional)\b/i.test(paragraph)) {
      return paragraph.trim().replace(/\s+/g, ' ');
    }
  }
  
  return '';
}

function extractCertifications(text: string): string[] {
  const certificationPatterns = [
    /(?:certified|certification|certificate)[\s\w.,()-]*(?:\d{4}|\d{2})/gi,
    /(?:AWS|Azure|Google Cloud|Microsoft|Oracle|Cisco|CompTIA)[\s\w.,()-]*(?:certified|certification|certificate)/gi,
    /(?:PMP|CISSP|CISM|CEH|CCNA|CCNP|CCIE)/gi
  ];
  
  const certifications = new Set<string>();
  
  certificationPatterns.forEach(pattern => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach(match => {
        const cleaned = match.trim().replace(/\s+/g, ' ');
        if (cleaned.length > 5 && cleaned.length < 100) {
          certifications.add(cleaned);
        }
      });
    }
  });
  
  return Array.from(certifications);
}

function extractProjects(text: string): string[] {
  const projectPatterns = [
    /(?:project|built|developed|created|designed)\s+[\w\s.,()-]+(?:using|with|in)\s+[\w\s.,()-]+/gi,
    /(?:projects?):\s*([\s\S]*?)(?:\n\s*\n|$)/gi
  ];

  const projects = new Set<string>();

  projectPatterns.forEach(pattern => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach(match => {
        const cleaned = match.trim().replace(/\s+/g, ' ');
        if (cleaned.length > 20 && cleaned.length < 200) {
          projects.add(cleaned);
        }
      });
    }
  });

  return Array.from(projects);
}

// PhD Scholar specific extraction functions
function extractPublications(text: string): Publication[] {
  const publications: Publication[] = [];

  // Only look for explicit publications sections
  const publicationSectionPatterns = [
    /(?:publications?|papers?|articles?|research\s+output)[:\-]?\s*([\s\S]*?)(?:\n\s*(?:[A-Z][A-Z\s]*[:\-]|$))/gi
  ];

  for (const pattern of publicationSectionPatterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      const sectionText = match[1];
      const lines = sectionText.split(/\n/)
        .map(p => p.trim())
        .filter(p => {
          // Only include lines that look like actual publications
          return p.length > 30 &&
                 p.length < 300 &&
                 (/\d{4}/.test(p) || p.includes('.') || p.includes(',')) &&
                 !p.toLowerCase().startsWith('skills') &&
                 !p.toLowerCase().startsWith('education') &&
                 !p.toLowerCase().startsWith('experience') &&
                 // Must contain publication-like keywords
                 (/(?:journal|conference|proceedings|published|paper|article)/i.test(p) ||
                  /(?:ieee|acm|springer|elsevier|nature|science)/i.test(p));
        });

      for (const line of lines) {
        const yearMatch = line.match(/\b(19|20)\d{2}\b/);
        if (!yearMatch) continue; // Skip if no year found

        const year = parseInt(yearMatch[0]);

        let type: Publication['type'] = 'other';
        if (line.toLowerCase().includes('journal')) type = 'journal';
        else if (line.toLowerCase().includes('conference')) type = 'conference';
        else if (line.toLowerCase().includes('book')) type = 'book';
        else if (line.toLowerCase().includes('chapter')) type = 'chapter';

        publications.push({
          title: line.replace(/^\d+\.\s*/, '').replace(/^[•\-\*]\s*/, '').trim(),
          authors: extractAuthorsFromPublication(line),
          journal: extractJournalFromPublication(line),
          year,
          type,
          citations: extractCitationsFromPublication(line),
          impact: determinePublicationImpact(line)
        });
      }
    }
  }
  return publications;
}

function extractConferences(text: string): Conference[] {
  const conferences: Conference[] = [];

  // Only look for explicit conference sections
  const conferenceSectionPatterns = [
    /(?:conferences?|presentations?|talks?|speaking)[:\-]?\s*([\s\S]*?)(?:\n\s*(?:[A-Z][A-Z\s]*[:\-]|$))/gi
  ];

  for (const pattern of conferenceSectionPatterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      const sectionText = match[1];
      const lines = sectionText.split(/\n/)
        .map(c => c.trim())
        .filter(c => {
          // Only include lines that look like actual conference entries
          return c.length > 20 &&
                 c.length < 250 &&
                 (/\d{4}/.test(c)) &&
                 !c.toLowerCase().startsWith('skills') &&
                 !c.toLowerCase().startsWith('education') &&
                 !c.toLowerCase().startsWith('experience') &&
                 // Must contain conference-like keywords
                 (/(?:conference|symposium|workshop|presented|talk|keynote)/i.test(c));
        });

      for (const line of lines) {
        const yearMatch = line.match(/\b(19|20)\d{2}\b/);
        if (!yearMatch) continue; // Skip if no year found

        const year = parseInt(yearMatch[0]);

        let type: Conference['type'] = 'presentation';
        if (line.toLowerCase().includes('poster')) type = 'poster';
        else if (line.toLowerCase().includes('keynote')) type = 'keynote';
        else if (line.toLowerCase().includes('workshop')) type = 'workshop';

        conferences.push({
          name: extractConferenceName(line),
          title: line.replace(/^\d+\.\s*/, '').replace(/^[•\-\*]\s*/, '').trim(),
          year,
          location: extractLocationFromConference(line),
          type,
          international: line.toLowerCase().includes('international') ||
                        line.toLowerCase().includes('global') ||
                        line.toLowerCase().includes('world')
        });
      }
    }
  }
  return conferences;
}

function extractResearchInterests(text: string): string[] {
  const patterns = [
    /(?:research interests?|research areas?|specialization)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:expertise|focus)[:\-]?\s*([^\n]+)/i
  ];

  let interests: string[] = [];
  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match) {
      const interestsText = match[1];
      interests = [...interests, ...interestsText.split(/[,;|•\-\n]/)
        .map(i => i.trim())
        .filter(i => i && i.length > 3)];
    }
  }
  return [...new Set(interests)];
}

function extractAwards(text: string): string[] {
  let awards: string[] = [];

  // Only look for explicit awards sections
  const awardsSectionPatterns = [
    /(?:awards?|honors?|recognitions?|achievements?)[:\-]?\s*([\s\S]*?)(?:\n\s*(?:[A-Z][A-Z\s]*[:\-]|$))/gi
  ];

  for (const pattern of awardsSectionPatterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      const sectionText = match[1];
      const lines = sectionText.split(/\n/)
        .map(a => a.trim())
        .filter(a => {
          // Only include lines that look like actual awards
          return a.length > 10 &&
                 a.length < 150 &&
                 !a.toLowerCase().startsWith('skills') &&
                 !a.toLowerCase().startsWith('education') &&
                 !a.toLowerCase().startsWith('experience') &&
                 // Should contain award-like content
                 (a.includes('award') || a.includes('prize') || a.includes('honor') ||
                  a.includes('recognition') || a.includes('fellowship') || /\d{4}/.test(a));
        });

      awards = [...awards, ...lines.map(a => a.replace(/^[•\-\*]\s*/, '').trim())];
    }
  }

  return [...new Set(awards)];
}

function extractGrants(text: string): Grant[] {
  const grants: Grant[] = [];
  const grantPatterns = [
    /(?:grants?|funding|sponsored)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:NSF|NIH|DOE|NASA|DARPA)[^\n]*/gi
  ];

  for (const pattern of grantPatterns) {
    const matches = text.match(pattern);
    if (matches) {
      const grantText = Array.isArray(matches) ? matches.join('\n') : matches;
      const lines = grantText.split(/\n|•|\-/)
        .map(g => g.trim())
        .filter(g => g && g.length > 10);

      for (const line of lines) {
        const yearMatch = line.match(/\b(19|20)\d{2}\b/);
        const year = yearMatch ? parseInt(yearMatch[0]) : new Date().getFullYear();

        let role: Grant['role'] = 'Collaborator';
        if (line.toLowerCase().includes('pi') || line.toLowerCase().includes('principal')) role = 'PI';
        else if (line.toLowerCase().includes('co-pi')) role = 'Co-PI';

        grants.push({
          title: line.replace(/^\d+\.\s*/, '').trim(),
          agency: extractAgencyFromGrant(line),
          amount: extractAmountFromGrant(line),
          year,
          role
        });
      }
    }
  }
  return grants;
}

function extractTeachingExperience(text: string): string[] {
  const patterns = [
    /(?:teaching|instructor|professor|lecturer)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:taught|course|class)[^\n]*/gi
  ];

  let teaching: string[] = [];
  for (const pattern of patterns) {
    const matches = text.match(pattern);
    if (matches) {
      const teachingText = Array.isArray(matches) ? matches.join('\n') : matches;
      teaching = [...teaching, ...teachingText.split(/\n|•|\-/)
        .map(t => t.trim())
        .filter(t => t && t.length > 10)
        .map(t => t.replace(/^[•\-\*]\s*/, ''))];
    }
  }
  return [...new Set(teaching)];
}

function extractSupervision(text: string): string[] {
  const patterns = [
    /(?:supervision|supervised|mentored|advisor)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:phd|master|undergraduate)\s+(?:student|thesis)[^\n]*/gi
  ];

  let supervision: string[] = [];
  for (const pattern of patterns) {
    const matches = text.match(pattern);
    if (matches) {
      const supervisionText = Array.isArray(matches) ? matches.join('\n') : matches;
      supervision = [...supervision, ...supervisionText.split(/\n|•|\-/)
        .map(s => s.trim())
        .filter(s => s && s.length > 5)
        .map(s => s.replace(/^[•\-\*]\s*/, ''))];
    }
  }
  return [...new Set(supervision)];
}

function extractPatents(text: string): string[] {
  const patterns = [
    /(?:patents?|intellectual property)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:patent|US\s*\d+)[^\n]*/gi
  ];

  let patents: string[] = [];
  for (const pattern of patterns) {
    const matches = text.match(pattern);
    if (matches) {
      const patentsText = Array.isArray(matches) ? matches.join('\n') : matches;
      patents = [...patents, ...patentsText.split(/\n|•|\-/)
        .map(p => p.trim())
        .filter(p => p && p.length > 10)
        .map(p => p.replace(/^[•\-\*]\s*/, ''))];
    }
  }
  return [...new Set(patents)];
}

function extractEditorialRoles(text: string): string[] {
  const patterns = [
    /(?:editorial|editor|reviewer|board)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:journal|conference)\s+(?:editor|reviewer)[^\n]*/gi
  ];

  let roles: string[] = [];
  for (const pattern of patterns) {
    const matches = text.match(pattern);
    if (matches) {
      const rolesText = Array.isArray(matches) ? matches.join('\n') : matches;
      roles = [...roles, ...rolesText.split(/\n|•|\-/)
        .map(r => r.trim())
        .filter(r => r && r.length > 5)
        .map(r => r.replace(/^[•\-\*]\s*/, ''))];
    }
  }
  return [...new Set(roles)];
}

function extractProfessionalMemberships(text: string): string[] {
  const patterns = [
    /(?:memberships?|member|society|association)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:IEEE|ACM|AAAI|SIGKDD)[^\n]*/gi
  ];

  let memberships: string[] = [];
  for (const pattern of patterns) {
    const matches = text.match(pattern);
    if (matches) {
      const membershipsText = Array.isArray(matches) ? matches.join('\n') : matches;
      memberships = [...memberships, ...membershipsText.split(/\n|•|\-/)
        .map(m => m.trim())
        .filter(m => m && m.length > 3)
        .map(m => m.replace(/^[•\-\*]\s*/, ''))];
    }
  }
  return [...new Set(memberships)];
}

function extractLanguages(text: string): string[] {
  const patterns = [
    /(?:languages?|linguistic)[:\-]?\s*([^\n]+)/i,
    /(?:fluent|native|proficient)\s+(?:in\s+)?([A-Za-z]+)/gi
  ];

  let languages: string[] = [];
  for (const pattern of patterns) {
    const matches = text.match(pattern);
    if (matches) {
      const languagesText = Array.isArray(matches) ? matches.join('\n') : matches;
      languages = [...languages, ...languagesText.split(/[,;|•\-\n]/)
        .map(l => l.trim())
        .filter(l => l && l.length > 2 && l.length < 20)];
    }
  }
  return [...new Set(languages)];
}

function extractResearchProjects(text: string): ResearchProject[] {
  const projects: ResearchProject[] = [];
  const projectPatterns = [
    /(?:research projects?|projects?)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
    /(?:project|research)[^\n]*(?:duration|period)[^\n]*/gi
  ];

  for (const pattern of projectPatterns) {
    const matches = text.match(pattern);
    if (matches) {
      const projectText = Array.isArray(matches) ? matches.join('\n') : matches;
      const lines = projectText.split(/\n|•|\-/)
        .map(p => p.trim())
        .filter(p => p && p.length > 20);

      for (const line of lines) {
        projects.push({
          title: line.replace(/^\d+\.\s*/, '').trim(),
          description: line,
          duration: extractDurationFromProject(line),
          role: extractRoleFromProject(line),
          technologies: extractTechnologiesFromProject(line)
        });
      }
    }
  }
  return projects;
}

function extractThesisTitle(text: string): string | undefined {
  const patterns = [
    /(?:thesis|dissertation)[:\-]?\s*([^\n]+)/i,
    /(?:phd|doctoral)\s+(?:thesis|dissertation)[:\-]?\s*([^\n]+)/i
  ];

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      return match[1].trim();
    }
  }
  return undefined;
}

function extractAdvisor(text: string): string | undefined {
  const patterns = [
    /(?:advisor|supervisor)[:\-]?\s*([^\n]+)/i,
    /(?:under|supervised by)[:\-]?\s*([^\n]+)/i
  ];

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      return match[1].trim();
    }
  }
  return undefined;
}

function extractInstitution(text: string): string | undefined {
  const patterns = [
    /(?:university|college|institute)[:\-]?\s*([^\n]+)/i,
    /(?:phd|doctoral)\s+(?:from|at)[:\-]?\s*([^\n]+)/i
  ];

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      return match[1].trim();
    }
  }
  return undefined;
}

function extractGraduationYear(text: string): number | undefined {
  const patterns = [
    /(?:graduated|graduation|phd|doctoral)[^\n]*(\b(19|20)\d{2}\b)/i,
    /(\b(19|20)\d{2}\b)[^\n]*(?:phd|doctoral|graduation)/i
  ];

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      return parseInt(match[1]);
    }
  }
  return undefined;
}

// Helper functions for publication parsing
function extractAuthorsFromPublication(line: string): string[] {
  // Simple author extraction - can be enhanced
  const authorPatterns = [
    /^([^,]+(?:,[^,]+)*),/,
    /by\s+([^,]+(?:,[^,]+)*)/i
  ];

  for (const pattern of authorPatterns) {
    const match = line.match(pattern);
    if (match) {
      return match[1].split(',').map(a => a.trim()).filter(a => a.length > 2);
    }
  }
  return ['Unknown'];
}

function extractJournalFromPublication(line: string): string {
  const journalPatterns = [
    /(?:in|published in)\s+([^,\n]+)/i,
    /([A-Z][^,\n]*(?:journal|conference|proceedings)[^,\n]*)/i
  ];

  for (const pattern of journalPatterns) {
    const match = line.match(pattern);
    if (match) {
      return match[1].trim();
    }
  }
  return 'Unknown';
}

function extractCitationsFromPublication(line: string): number | undefined {
  const citationPattern = /(\d+)\s*citations?/i;
  const match = line.match(citationPattern);
  return match ? parseInt(match[1]) : undefined;
}

function determinePublicationImpact(line: string): 'high' | 'medium' | 'low' {
  const highImpactKeywords = ['nature', 'science', 'cell', 'lancet', 'nejm', 'top-tier', 'prestigious'];
  const mediumImpactKeywords = ['ieee', 'acm', 'springer', 'elsevier'];

  const lowerLine = line.toLowerCase();

  if (highImpactKeywords.some(keyword => lowerLine.includes(keyword))) {
    return 'high';
  } else if (mediumImpactKeywords.some(keyword => lowerLine.includes(keyword))) {
    return 'medium';
  }
  return 'low';
}

// Helper functions for conference parsing
function extractConferenceName(line: string): string {
  const conferencePatterns = [
    /(?:at|in)\s+([^,\n]+(?:conference|symposium|workshop)[^,\n]*)/i,
    /([A-Z][^,\n]*(?:conference|symposium|workshop)[^,\n]*)/i
  ];

  for (const pattern of conferencePatterns) {
    const match = line.match(pattern);
    if (match) {
      return match[1].trim();
    }
  }
  return 'Unknown Conference';
}

function extractLocationFromConference(line: string): string | undefined {
  const locationPatterns = [
    /(?:in|at)\s+([A-Z][a-z]+(?:,\s*[A-Z][a-z]+)*)/,
    /([A-Z][a-z]+,\s*[A-Z]{2,3})/
  ];

  for (const pattern of locationPatterns) {
    const match = line.match(pattern);
    if (match) {
      return match[1].trim();
    }
  }
  return undefined;
}

// Helper functions for grant parsing
function extractAgencyFromGrant(line: string): string {
  const agencies = ['NSF', 'NIH', 'DOE', 'NASA', 'DARPA', 'DOD', 'USDA'];
  for (const agency of agencies) {
    if (line.toUpperCase().includes(agency)) {
      return agency;
    }
  }
  return 'Unknown Agency';
}

function extractAmountFromGrant(line: string): string | undefined {
  const amountPattern = /\$[\d,]+(?:\.\d{2})?/;
  const match = line.match(amountPattern);
  return match ? match[0] : undefined;
}

// Helper functions for research project parsing
function extractDurationFromProject(line: string): string {
  const durationPatterns = [
    /(\d{4}\s*-\s*\d{4})/,
    /(\d{1,2}\s+(?:months?|years?))/i,
    /(?:duration|period)[:\-]?\s*([^\n,]+)/i
  ];

  for (const pattern of durationPatterns) {
    const match = line.match(pattern);
    if (match) {
      return match[1].trim();
    }
  }
  return 'Unknown';
}

function extractRoleFromProject(line: string): string {
  const rolePatterns = [
    /(?:role|position)[:\-]?\s*([^\n,]+)/i,
    /(lead|principal|co-investigator|researcher)/i
  ];

  for (const pattern of rolePatterns) {
    const match = line.match(pattern);
    if (match) {
      return match[1].trim();
    }
  }
  return 'Researcher';
}

function extractTechnologiesFromProject(line: string): string[] | undefined {
  const techPatterns = [
    /(?:using|with|technologies?)[:\-]?\s*([^\n]+)/i,
    /(Python|Java|C\+\+|JavaScript|R|MATLAB|TensorFlow|PyTorch)[^\n]*/gi
  ];

  let technologies: string[] = [];
  for (const pattern of techPatterns) {
    const matches = line.match(pattern);
    if (matches) {
      const techText = Array.isArray(matches) ? matches.join(' ') : matches;
      technologies = [...technologies, ...techText.split(/[,;|]/)
        .map(t => t.trim())
        .filter(t => t && t.length > 2)];
    }
  }
  return technologies.length > 0 ? [...new Set(technologies)] : undefined;
}

// Academic metrics calculation functions
function calculateHIndex(publications: Publication[]): number {
  if (!publications || publications.length === 0) return 0;

  const citations = publications
    .map(pub => pub.citations || 0)
    .sort((a, b) => b - a);

  let hIndex = 0;
  for (let i = 0; i < citations.length; i++) {
    if (citations[i] >= i + 1) {
      hIndex = i + 1;
    } else {
      break;
    }
  }
  return hIndex;
}

function calculateCitationCount(publications: Publication[]): number {
  if (!publications || publications.length === 0) return 0;

  return publications.reduce((total, pub) => total + (pub.citations || 0), 0);
}

function calculateTotalScore(data: {
  publications: Publication[];
  conferences: Conference[];
  awards: string[];
  grants: Grant[];
  hIndex: number;
  citationCount: number;
  teachingExperience: string[];
  supervision: string[];
}): number {
  let score = 0;

  // Publications scoring
  score += data.publications.length * 10;
  data.publications.forEach(pub => {
    if (pub.type === 'journal') score += 15;
    else if (pub.type === 'conference') score += 10;
    else if (pub.type === 'book') score += 20;

    if (pub.impact === 'high') score += 25;
    else if (pub.impact === 'medium') score += 15;
    else score += 5;
  });

  // Conference scoring
  score += data.conferences.length * 8;
  data.conferences.forEach(conf => {
    if (conf.international) score += 10;
    if (conf.type === 'keynote') score += 15;
    else if (conf.type === 'presentation') score += 8;
    else score += 5;
  });

  // Other factors
  score += data.awards.length * 12;
  score += data.grants.length * 20;
  score += data.hIndex * 5;
  score += Math.min(data.citationCount / 10, 50); // Cap citation bonus
  score += data.teachingExperience.length * 5;
  score += data.supervision.length * 8;

  return Math.round(score);
}

// Ranking and filtering functions for PhD scholars
export function rankCandidatesByScore(candidates: ExtractedData[]): ExtractedData[] {
  return candidates
    .filter(candidate => candidate.totalScore !== undefined)
    .sort((a, b) => (b.totalScore || 0) - (a.totalScore || 0));
}

export function getTopNCandidates(candidates: ExtractedData[], n: number): ExtractedData[] {
  const rankedCandidates = rankCandidatesByScore(candidates);
  return rankedCandidates.slice(0, n);
}

export function filterCandidatesByMinScore(candidates: ExtractedData[], minScore: number): ExtractedData[] {
  return candidates.filter(candidate => (candidate.totalScore || 0) >= minScore);
}

export function getCandidatesByResearchArea(candidates: ExtractedData[], researchArea: string): ExtractedData[] {
  return candidates.filter(candidate =>
    candidate.researchInterests.some(interest =>
      interest.toLowerCase().includes(researchArea.toLowerCase())
    )
  );
}

// Enhanced metadata extraction with comprehensive PhD scholar focus
export async function extractPhDScholarMetadata(file: File): Promise<ExtractedData> {
  const metadata = await extractMetadata(file);
  return validateAndCleanMetadata(metadata);
}

// Validation function to ensure only meaningful data is extracted
function validateAndCleanMetadata(data: ExtractedData): ExtractedData {
  return {
    ...data,
    // Clean experience - remove entries that are too generic or likely false positives
    experience: data.experience.filter(exp => {
      const lower = exp.toLowerCase();
      return exp.length > 20 &&
             exp.length < 200 &&
             !lower.includes('lorem ipsum') &&
             !lower.includes('sample') &&
             !lower.includes('example') &&
             (/\d{4}/.test(exp) || exp.includes(' at ') || exp.includes(' - '));
    }),

    // Clean education - ensure it contains actual degree or institution info
    education: data.education.filter(edu => {
      const lower = edu.toLowerCase();
      return edu.length > 15 &&
             edu.length < 200 &&
             !lower.includes('lorem ipsum') &&
             !lower.includes('sample') &&
             (lower.includes('university') || lower.includes('college') ||
              lower.includes('institute') || lower.includes('bachelor') ||
              lower.includes('master') || lower.includes('phd') ||
              lower.includes('doctorate'));
    }),

    // Clean publications - ensure they look like real publications
    publications: data.publications.filter(pub => {
      return pub.title.length > 20 &&
             pub.title.length < 300 &&
             pub.year >= 1990 &&
             pub.year <= new Date().getFullYear() + 1 &&
             !pub.title.toLowerCase().includes('lorem ipsum') &&
             !pub.title.toLowerCase().includes('sample');
    }),

    // Clean conferences - ensure they look like real conferences
    conferences: data.conferences.filter(conf => {
      return conf.title.length > 15 &&
             conf.title.length < 250 &&
             conf.year >= 1990 &&
             conf.year <= new Date().getFullYear() + 1 &&
             !conf.title.toLowerCase().includes('lorem ipsum') &&
             !conf.title.toLowerCase().includes('sample');
    }),

    // Clean awards - remove generic or empty awards
    awards: data.awards.filter(award => {
      const lower = award.toLowerCase();
      return award.length > 8 &&
             award.length < 150 &&
             !lower.includes('lorem ipsum') &&
             !lower.includes('sample') &&
             !lower.includes('example') &&
             award.trim() !== '';
    }),

    // Clean other arrays
    researchInterests: data.researchInterests.filter(interest =>
      interest.length > 3 && interest.length < 100 && interest.trim() !== ''
    ),
    grants: data.grants.filter(grant =>
      grant.title.length > 10 && grant.title.length < 200
    ),
    teachingExperience: data.teachingExperience.filter(exp =>
      exp.length > 10 && exp.length < 150 && exp.trim() !== ''
    ),
    supervision: data.supervision.filter(sup =>
      sup.length > 5 && sup.length < 150 && sup.trim() !== ''
    ),
    patents: data.patents.filter(patent =>
      patent.length > 10 && patent.length < 200 && patent.trim() !== ''
    ),
    editorialRoles: data.editorialRoles.filter(role =>
      role.length > 5 && role.length < 150 && role.trim() !== ''
    ),
    professionalMemberships: data.professionalMemberships.filter(membership =>
      membership.length > 3 && membership.length < 100 && membership.trim() !== ''
    ),
    languages: data.languages.filter(lang =>
      lang.length > 2 && lang.length < 30 && lang.trim() !== ''
    ),
    researchProjects: data.researchProjects.filter(project =>
      project.title.length > 10 && project.title.length < 200
    )
  };
}