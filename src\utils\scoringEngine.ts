import { ExtractedData } from './metadataExtractor';

export interface Scores {
  recruiterScore: number;
  analystScore: number;
  hrScore: number;
  finalScore: number;
  breakdown: {
    skillsMatch: number;
    experienceMatch: number;
    educationMatch: number;
    keywordMatch: number;
    qualityScore: number;
  };
}

export function scoreResume(resumeData: ExtractedData, jobDescription: string): Scores {
  const jobLower = jobDescription.toLowerCase();
  
  // Extract key requirements from job description
  const requiredSkills = extractRequiredSkills(jobDescription);
  const experienceYears = extractExperienceRequirement(jobDescription);
  const educationLevel = extractEducationRequirement(jobDescription);
  const keywords = extractKeywords(jobDescription);
  
  // Calculate individual scores
  const skillsMatch = calculateSkillsMatch(resumeData.skills, requiredSkills);
  const experienceMatch = calculateExperienceMatch(resumeData, experienceYears, jobLower);
  const educationMatch = calculateEducationMatch(resumeData.education, educationLevel);
  const keywordMatch = calculateKeywordMatch(resumeData, keywords);
  const qualityScore = calculateQualityScore(resumeData);
  
  // Weighted scoring for different agents
  const recruiterScore = Math.min(100, (skillsMatch * 0.4 + experienceMatch * 0.3 + qualityScore * 0.3));
  const analystScore = Math.min(100, (keywordMatch * 0.4 + skillsMatch * 0.3 + experienceMatch * 0.3));
  const hrScore = Math.min(100, (qualityScore * 0.4 + educationMatch * 0.3 + experienceMatch * 0.3));
  
  // Final score with weighted average
  const finalScore = (recruiterScore * 0.4 + analystScore * 0.35 + hrScore * 0.25);
  
  return {
    recruiterScore: Math.round(recruiterScore * 100) / 100,
    analystScore: Math.round(analystScore * 100) / 100,
    hrScore: Math.round(hrScore * 100) / 100,
    finalScore: Math.round(finalScore * 100) / 100,
    breakdown: {
      skillsMatch: Math.round(skillsMatch * 100) / 100,
      experienceMatch: Math.round(experienceMatch * 100) / 100,
      educationMatch: Math.round(educationMatch * 100) / 100,
      keywordMatch: Math.round(keywordMatch * 100) / 100,
      qualityScore: Math.round(qualityScore * 100) / 100
    }
  };
}

function extractRequiredSkills(jobDescription: string): string[] {
  const skillsSection = jobDescription.match(/(?:skills|requirements|qualifications|technologies|tools|experience with):(.*?)(?:\n\n|$)/is);
  const skills: string[] = [];
  
  if (skillsSection) {
    const text = skillsSection[1];
    // Common tech skills patterns
    const techSkills = [
      'JavaScript', 'TypeScript', 'Python', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust',
      'React', 'Vue', 'Angular', 'Node.js', 'Express', 'Django', 'Flask', 'Spring',
      'AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'Git', 'SQL', 'MongoDB', 'PostgreSQL',
      'HTML', 'CSS', 'REST', 'GraphQL', 'Microservices', 'Agile', 'Scrum'
    ];
    
    techSkills.forEach(skill => {
      if (new RegExp(`\\b${skill}\\b`, 'i').test(text)) {
        skills.push(skill);
      }
    });
  }
  
  return skills;
}

function extractExperienceRequirement(jobDescription: string): number {
  const experienceMatch = jobDescription.match(/(\d+)[\s+]?(?:years?|yrs?)[\s+]?(?:of\s+)?experience/i);
  return experienceMatch ? parseInt(experienceMatch[1]) : 0;
}

function extractEducationRequirement(jobDescription: string): string {
  const educationPatterns = ['PhD', 'Ph.D', 'Master', 'Bachelor', 'degree'];
  
  for (const pattern of educationPatterns) {
    if (new RegExp(`\\b${pattern}\\b`, 'i').test(jobDescription)) {
      return pattern.toLowerCase();
    }
  }
  
  return '';
}

function extractKeywords(jobDescription: string): string[] {
  const text = jobDescription.toLowerCase();
  const keywords: string[] = [];
  
  // Important action words and concepts
  const importantTerms = [
    'develop', 'design', 'implement', 'manage', 'lead', 'architect', 'optimize',
    'collaborate', 'mentor', 'scale', 'deploy', 'maintain', 'troubleshoot',
    'api', 'database', 'frontend', 'backend', 'full-stack', 'cloud', 'devops',
    'testing', 'security', 'performance', 'mobile', 'web', 'application'
  ];
  
  importantTerms.forEach(term => {
    if (text.includes(term)) {
      keywords.push(term);
    }
  });
  
  return keywords;
}

function calculateSkillsMatch(candidateSkills: string[], requiredSkills: string[]): number {
  if (requiredSkills.length === 0) return 70; // Default score if no specific skills identified
  
  const matchedSkills = candidateSkills.filter(skill => 
    requiredSkills.some(required => 
      skill.toLowerCase().includes(required.toLowerCase()) ||
      required.toLowerCase().includes(skill.toLowerCase())
    )
  );
  
  const matchPercentage = (matchedSkills.length / requiredSkills.length) * 100;
  
  // Bonus for having many relevant skills
  const bonusScore = Math.min(20, candidateSkills.length * 2);
  
  return Math.min(100, matchPercentage + bonusScore);
}

function calculateExperienceMatch(resumeData: ExtractedData, requiredYears: number, jobDescription: string): number {
  let experienceScore = 60; // Base score
  
  // Estimate years of experience from the resume
  const experienceYears = estimateExperienceYears(resumeData);
  
  if (requiredYears > 0) {
    if (experienceYears >= requiredYears) {
      experienceScore = 90 + Math.min(10, (experienceYears - requiredYears) * 2);
    } else {
      experienceScore = 50 + (experienceYears / requiredYears) * 40;
    }
  }
  
  // Bonus for relevant job titles
  const relevantTitles = ['engineer', 'developer', 'programmer', 'analyst', 'architect', 'lead', 'senior'];
  const hasRelevantTitle = resumeData.experience.some(exp => 
    relevantTitles.some(title => exp.toLowerCase().includes(title))
  );
  
  if (hasRelevantTitle) {
    experienceScore += 10;
  }
  
  return Math.min(100, experienceScore);
}

function calculateEducationMatch(education: string[], requiredLevel: string): number {
  if (!requiredLevel) return 75; // Default score if no specific requirement
  
  const hasRequiredEducation = education.some(edu => {
    const eduLower = edu.toLowerCase();
    return eduLower.includes(requiredLevel) || 
           (requiredLevel === 'bachelor' && (eduLower.includes('b.') || eduLower.includes('bs') || eduLower.includes('ba'))) ||
           (requiredLevel === 'master' && (eduLower.includes('m.') || eduLower.includes('ms') || eduLower.includes('ma')));
  });
  
  if (hasRequiredEducation) {
    return 95;
  }
  
  // Partial credit for any degree
  const hasAnyDegree = education.length > 0;
  return hasAnyDegree ? 60 : 30;
}

function calculateKeywordMatch(resumeData: ExtractedData, keywords: string[]): number {
  if (keywords.length === 0) return 70;
  
  const resumeText = [
    resumeData.summary,
    ...resumeData.experience,
    ...resumeData.skills,
    ...resumeData.projects
  ].join(' ').toLowerCase();
  
  const matchedKeywords = keywords.filter(keyword => 
    resumeText.includes(keyword.toLowerCase())
  );
  
  return Math.min(100, (matchedKeywords.length / keywords.length) * 100);
}

function calculateQualityScore(resumeData: ExtractedData): number {
  let score = 50; // Base score
  
  // Completeness factors
  if (resumeData.name !== 'N/A' && resumeData.name.length > 3) score += 10;
  if (resumeData.email !== 'N/A') score += 10;
  if (resumeData.phone !== 'N/A') score += 5;
  if (resumeData.skills.length >= 5) score += 15;
  if (resumeData.education.length > 0) score += 10;
  if (resumeData.experience.length > 0) score += 15;
  if (resumeData.summary.length > 50) score += 10;
  if (resumeData.projects.length > 0) score += 5;
  if (resumeData.certifications.length > 0) score += 5;
  
  // Quality factors
  if (resumeData.skills.length >= 10) score += 5; // Diverse skill set
  if (resumeData.experience.length >= 3) score += 5; // Multiple experiences
  
  return Math.min(100, score);
}

function estimateExperienceYears(resumeData: ExtractedData): number {
  // Simple heuristic based on experience entries and education
  let years = 0;
  
  // Base years from experience entries
  years = resumeData.experience.length * 1.5;
  
  // Look for year patterns in experience
  const yearMatches = resumeData.experience.join(' ').match(/\b(19|20)\d{2}\b/g);
  if (yearMatches && yearMatches.length >= 2) {
    const years_from_dates = yearMatches.map(y => parseInt(y));
    const earliest = Math.min(...years_from_dates);
    const latest = Math.max(...years_from_dates);
    const estimated = new Date().getFullYear() - earliest;
    years = Math.max(years, estimated);
  }
  
  return Math.min(years, 20); // Cap at 20 years
}