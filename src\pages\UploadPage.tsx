import React, { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useApp } from '../context/AppContext';
import { Upload, X, FileText, AlertCircle, CheckCircle, Users, Zap } from 'lucide-react';

const UploadPage: React.FC = () => {
  const { state, dispatch } = useApp();
  const navigate = useNavigate();
  const [dragActive, setDragActive] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>(state.uploadedFiles);
  const [errors, setErrors] = useState<string[]>([]);

  const validateFile = (file: File): string | null => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = [
      'application/pdf', 
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      return `${file.name}: Only PDF and DOCX files are supported`;
    }
    
    if (file.size > maxSize) {
      return `${file.name}: File size must be less than 10MB`;
    }
    
    return null;
  };

  const handleFiles = useCallback((files: FileList) => {
    const fileArray = Array.from(files);
    const validFiles: File[] = [];
    const newErrors: string[] = [];

    // Check for duplicates
    const existingNames = uploadedFiles.map(f => f.name);

    fileArray.forEach(file => {
      if (existingNames.includes(file.name)) {
        newErrors.push(`${file.name}: File already uploaded`);
        return;
      }

      const error = validateFile(file);
      if (error) {
        newErrors.push(error);
      } else {
        validFiles.push(file);
      }
    });

    setErrors(newErrors);
    setUploadedFiles(prev => [...prev, ...validFiles]);
  }, [uploadedFiles]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDragIn = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  }, []);

  const handleDragOut = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files);
    }
  }, [handleFiles]);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files);
    }
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
    setErrors([]);
  };

  const clearAllFiles = () => {
    setUploadedFiles([]);
    setErrors([]);
  };

  const handleContinue = () => {
    if (uploadedFiles.length === 0) {
      setErrors(['Please upload at least one resume file']);
      return;
    }
    
    dispatch({ type: 'SET_UPLOADED_FILES', payload: uploadedFiles });
    navigate('/job-description');
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="min-h-screen py-12 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="relative">
              <Upload className="w-16 h-16 text-blue-600" />
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center">
                <Zap className="w-3 h-3 text-white" />
              </div>
            </div>
          </div>
          
          <h1 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
            Upload Resume Files
          </h1>
          <p className="text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Upload multiple PDF or DOCX resume files for AI-powered analysis. 
            Our system supports batch processing of 50+ resumes with enterprise-grade performance.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Upload Area */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
              <div
                className={`relative border-2 border-dashed rounded-2xl p-12 text-center transition-all duration-300 ${
                  dragActive
                    ? 'border-blue-400 bg-blue-50 scale-105'
                    : uploadedFiles.length > 0
                    ? 'border-emerald-300 bg-emerald-50'
                    : 'border-slate-300 bg-slate-50 hover:border-blue-400 hover:bg-blue-50'
                }`}
                onDragEnter={handleDragIn}
                onDragLeave={handleDragOut}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <input
                  type="file"
                  multiple
                  accept=".pdf,.docx,.doc"
                  onChange={handleFileInput}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
                
                <div className="space-y-6">
                  <div className={`w-20 h-20 mx-auto rounded-full flex items-center justify-center transition-all duration-300 ${
                    uploadedFiles.length > 0 ? 'bg-emerald-100' : 'bg-blue-100'
                  }`}>
                    {uploadedFiles.length > 0 ? (
                      <CheckCircle className="w-10 h-10 text-emerald-600" />
                    ) : (
                      <Upload className="w-10 h-10 text-blue-600" />
                    )}
                  </div>
                  
                  <div>
                    <h3 className="text-2xl font-semibold text-slate-900 mb-3">
                      {uploadedFiles.length > 0 
                        ? `${uploadedFiles.length} file${uploadedFiles.length !== 1 ? 's' : ''} uploaded`
                        : 'Drag and drop your resume files here'
                      }
                    </h3>
                    <p className="text-slate-600 mb-2">
                      or <span className="text-blue-600 font-medium cursor-pointer hover:text-blue-700">click to browse</span>
                    </p>
                    <p className="text-sm text-slate-500">
                      Supports PDF and DOCX files up to 10MB each • Batch upload up to 50+ resumes
                    </p>
                  </div>
                </div>
              </div>

              {/* File Management */}
              {uploadedFiles.length > 0 && (
                <div className="flex justify-between items-center mt-6">
                  <div className="text-sm text-slate-600">
                    Total: {uploadedFiles.length} files ({formatFileSize(uploadedFiles.reduce((acc, file) => acc + file.size, 0))})
                  </div>
                  <button
                    onClick={clearAllFiles}
                    className="text-sm text-red-600 hover:text-red-700 font-medium"
                  >
                    Clear All
                  </button>
                </div>
              )}
            </div>

            {/* Error Messages */}
            {errors.length > 0 && (
              <div className="bg-white rounded-2xl shadow-lg p-6 mb-8 border-l-4 border-red-500">
                <div className="flex items-center mb-3">
                  <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
                  <h4 className="font-semibold text-red-900">Upload Issues</h4>
                </div>
                <ul className="text-sm text-red-700 space-y-1">
                  {errors.map((error, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-red-500 mr-2">•</span>
                      <span>{error}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Continue Button */}
            <div className="text-center">
              <button
                onClick={handleContinue}
                disabled={uploadedFiles.length === 0}
                className={`inline-flex items-center px-8 py-4 font-semibold rounded-xl transition-all duration-200 ${
                  uploadedFiles.length > 0
                    ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-xl transform hover:-translate-y-1'
                    : 'bg-slate-300 text-slate-500 cursor-not-allowed'
                }`}
              >
                Continue to Job Description
                <FileText className="w-5 h-5 ml-2" />
              </button>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Upload Stats */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="font-semibold text-slate-900 mb-4 flex items-center">
                <Users className="w-5 h-5 mr-2 text-blue-600" />
                Upload Statistics
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-slate-600">Files Uploaded</span>
                  <span className="font-semibold text-slate-900">{uploadedFiles.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-600">Total Size</span>
                  <span className="font-semibold text-slate-900">
                    {formatFileSize(uploadedFiles.reduce((acc, file) => acc + file.size, 0))}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-600">Status</span>
                  <span className={`font-semibold ${uploadedFiles.length > 0 ? 'text-emerald-600' : 'text-slate-500'}`}>
                    {uploadedFiles.length > 0 ? 'Ready' : 'Waiting'}
                  </span>
                </div>
              </div>
            </div>

            {/* Tips */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="font-semibold text-slate-900 mb-4">💡 Upload Tips</h3>
              <ul className="text-sm text-slate-600 space-y-2">
                <li className="flex items-start">
                  <CheckCircle className="w-4 h-4 text-emerald-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Upload high-quality PDF or DOCX files for best results</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-4 h-4 text-emerald-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Ensure resumes are text-based, not scanned images</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-4 h-4 text-emerald-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Our AI works best with standard resume formats</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-4 h-4 text-emerald-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Batch upload supported for enterprise workflows</span>
                </li>
              </ul>
            </div>

            {/* Supported Formats */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="font-semibold text-slate-900 mb-4">Supported Formats</h3>
              <div className="space-y-3">
                <div className="flex items-center p-3 bg-slate-50 rounded-lg">
                  <FileText className="w-5 h-5 text-red-500 mr-3" />
                  <div>
                    <div className="font-medium text-slate-900">PDF</div>
                    <div className="text-xs text-slate-600">Portable Document Format</div>
                  </div>
                </div>
                <div className="flex items-center p-3 bg-slate-50 rounded-lg">
                  <FileText className="w-5 h-5 text-blue-500 mr-3" />
                  <div>
                    <div className="font-medium text-slate-900">DOCX</div>
                    <div className="text-xs text-slate-600">Microsoft Word Document</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Uploaded Files List */}
        {uploadedFiles.length > 0 && (
          <div className="mt-12">
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
              <div className="p-6 border-b border-slate-200">
                <h3 className="text-xl font-semibold text-slate-900">
                  Uploaded Files ({uploadedFiles.length})
                </h3>
              </div>
              
              <div className="max-h-96 overflow-y-auto">
                <div className="divide-y divide-slate-100">
                  {uploadedFiles.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-4 hover:bg-slate-50 transition-colors duration-200">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <FileText className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium text-slate-900 truncate max-w-xs">{file.name}</p>
                          <p className="text-sm text-slate-600">
                            {formatFileSize(file.size)} • {file.type.includes('pdf') ? 'PDF' : 'DOCX'}
                          </p>
                        </div>
                      </div>
                      
                      <button
                        onClick={() => removeFile(index)}
                        className="p-2 text-slate-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200"
                        title="Remove file"
                      >
                        <X className="w-5 h-5" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UploadPage;