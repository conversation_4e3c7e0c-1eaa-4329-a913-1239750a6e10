import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useApp } from '../context/AppContext';
import { langchainService } from '../services/langchainService';
import { multiAgentService } from '../services/multiAgentService';
import { Brain, CheckCircle, Clock, AlertCircle, Zap, Users, Target, Shield, Award, FileText } from 'lucide-react';
import { ExtractedData, ResumeResult } from '../types';

const ExtractionPage: React.FC = () => {
  const { state, dispatch } = useApp();
  const navigate = useNavigate();
  const location = useLocation();

  const [currentStep, setCurrentStep] = useState('Initializing AI agents...');
  const [progress, setProgress] = useState(0);
  const [processedCount, setProcessedCount] = useState(0);
  const [isAnalyzing, setIsAnalyzing] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentAgent, setCurrentAgent] = useState('');
  const [extractionDetails, setExtractionDetails] = useState<string[]>([]);

  const totalFiles = state.uploadedFiles.length;

  useEffect(() => {
    if (state.uploadedFiles.length === 0) {
      navigate('/upload');
      return;
    }

    if (!state.jobDescription) {
      navigate('/job-description');
      return;
    }

    analyzeResumes();
  }, []);

  const analyzeResumes = async () => {
    try {
      setCurrentStep('🚀 Initializing LangChain RAG system...');
      setProgress(5);

      // Phase 1: Extract content using LangChain
      setCurrentStep('📄 Extracting resume content with AI-powered parsing...');
      setProgress(10);

      const extractionPromises = state.uploadedFiles.map(async (file, index) => {
        try {
          setCurrentStep(`📄 Processing ${file.name}...`);
          setExtractionDetails(prev => [...prev, `Starting extraction for ${file.name}`]);
          
          // Extract raw content using LangChain
          const rawContent = await langchainService.extractDocumentContent(file);
          
          if (!rawContent || rawContent.trim().length < 20) {
            throw new Error(`Failed to extract meaningful content from ${file.name}`);
          }

          setExtractionDetails(prev => [...prev, `✓ Extracted ${rawContent.length} characters from ${file.name}`]);
          
          // Store extracted content
          dispatch({
            type: 'SET_EXTRACTED_CONTENT',
            payload: { fileId: `${index}-${file.name}`, content: rawContent }
          });

          // Extract structured data using AI
          const extractedData = await langchainService.extractStructuredData(rawContent, file.name);
          
          if (!extractedData.name || extractedData.name === 'N/A') {
            extractedData.name = file.name.replace(/\.[^/.]+$/, '');
          }

          setExtractionDetails(prev => [...prev, `✓ Parsed metadata for ${extractedData.name}: ${extractedData.skills.length} skills, ${extractedData.totalExperienceYears} years experience`]);
          
          const finalExtractedData: ExtractedData = {
            ...extractedData,
            workExperience: extractedData.workExperience || []
          };

          setProcessedCount(index + 1);
          return {
            id: `${index}-${Date.now()}`,
            file,
            extractedData: finalExtractedData,
            index
          };
        } catch (error) {
          console.error(`Error processing file ${file.name}:`, error);
          setExtractionDetails(prev => [...prev, `❌ Error processing ${file.name}: ${error.message}`]);
          
          return {
            id: `${index}-${Date.now()}`,
            file,
            extractedData: getDefaultExtractedData(file.name),
            index
          };
        }
      });

      const extractedResults = await Promise.all(extractionPromises);
      setProgress(30);

      // Validate extraction results
      const validResults = extractedResults.filter(result => 
        result.extractedData && result.extractedData.name !== 'N/A'
      );

      if (validResults.length === 0) {
        throw new Error('Failed to extract data from any resume. Please check file formats and content.');
      }

      setExtractionDetails(prev => [...prev, `✅ Successfully extracted data from ${validResults.length}/${totalFiles} resumes`]);

      // Phase 2: Multi-Agent Analysis
      setCurrentStep('🤖 Running multi-agent analysis...');
      setCurrentAgent('Recruiter Agent');
      setProgress(40);

      const analysisPromises = validResults.map(async (result, index) => {
        try {
          setCurrentStep(`🤖 AI analysis for ${result.extractedData.name}...`);
          
          // Cycle through agent indicators
          const agents = ['Recruiter', 'Analyst', 'HR', 'Recommender'];
          setCurrentAgent(`${agents[index % agents.length]} Agent`);

          setExtractionDetails(prev => [...prev, `🤖 Starting multi-agent analysis for ${result.extractedData.name}`]);

          const agentResult = await multiAgentService.analyzeCandidate(
            result.extractedData,
            state.jobDescription!,
            result.id
          );

          setExtractionDetails(prev => [...prev, `✅ Analysis complete for ${result.extractedData.name}: R:${agentResult.scores.recruiterScore} A:${agentResult.scores.analystScore} HR:${agentResult.scores.hrScore} Final:${agentResult.scores.recommenderScore}`]);

          return {
            ...result,
            scores: agentResult.scores,
            breakdown: agentResult.breakdown,
            analysis: agentResult.analysis,
            rank: 0,
            isTopCandidate: false
          };
        } catch (error) {
          console.error(`Error analyzing candidate ${result.id}:`, error);
          setExtractionDetails(prev => [...prev, `❌ Analysis failed for ${result.extractedData.name}: ${error.message}`]);
          
          return {
            ...result,
            scores: getDefaultScores(),
            breakdown: getDefaultBreakdown(),
            analysis: getDefaultAnalysis(),
            rank: 0,
            isTopCandidate: false
          };
        }
      });

      const analyzedResults = await Promise.all(analysisPromises);
      setProgress(70);

      // Phase 3: Ranking and Final Processing
      setCurrentStep('📊 Calculating final rankings...');
      setProgress(80);

      // Calculate final scores and rank candidates
      const rankedResults = analyzedResults
        .map(result => ({
          ...result,
          finalScore: calculateFinalScore(result.scores)
        }))
        .sort((a, b) => b.finalScore - a.finalScore)
        .map((result, index) => ({
          ...result,
          rank: index + 1,
          isTopCandidate: index < state.topN
        }));

      setProgress(90);
      setCurrentStep('✅ Analysis complete! Preparing results...');

      setExtractionDetails(prev => [...prev, `🎯 Final ranking complete. Top ${state.topN} candidates selected for detailed feedback.`]);

      // Update global state
      dispatch({ type: 'SET_RESULTS', payload: rankedResults });
      
      setProgress(100);
      setCurrentStep('🎉 Ready to view results!');

      // Small delay to show completion
      setTimeout(() => {
        setIsAnalyzing(false);
        navigate('/results');
      }, 2000);

    } catch (error) {
      console.error('Error during analysis:', error);
      setError(`Analysis failed: ${error.message}. Please check your resume files and try again.`);
      setIsAnalyzing(false);
    }
  };

  const calculateFinalScore = (scores: any): number => {
    return Math.round(
      scores.recruiterScore * 0.3 +
      scores.analystScore * 0.3 +
      scores.hrScore * 0.2 +
      scores.recommenderScore * 0.2
    );
  };

  const getDefaultExtractedData = (fileName: string): ExtractedData => ({
    name: fileName.replace(/\.[^/.]+$/, ""),
    email: 'N/A',
    phone: 'N/A',
    skills: [],
    education: [],
    experience: [],
    summary: '',
    certifications: [],
    projects: [],
    workExperience: [],
    totalExperienceYears: 0
  });

  const getDefaultScores = () => ({
    recruiterScore: 70,
    analystScore: 70,
    hrScore: 70,
    recommenderScore: 70
  });

  const getDefaultBreakdown = () => ({
    skillsMatch: 65,
    experienceMatch: 70,
    educationMatch: 70,
    projectMatch: 65,
    communicationScore: 75,
    overallFit: 70
  });

  const getDefaultAnalysis = () => ({
    recruiterAnalysis: 'Technical assessment completed with standard evaluation.',
    analystAnalysis: 'Job fit analysis completed with role alignment assessment.',
    hrAnalysis: 'HR evaluation completed with communication assessment.',
    recommenderAnalysis: 'Comprehensive candidate evaluation completed.',
    finalRecommendation: 'Consider for interview based on overall qualifications.',
    strengths: ['Relevant background', 'Professional presentation'],
    weaknesses: ['Limited specific details available'],
    fitScore: 70
  });

  const steps = [
    { name: 'Content Extraction', completed: progress >= 30, icon: FileText },
    { name: 'Multi-Agent Analysis', completed: progress >= 70, icon: Brain },
    { name: 'Ranking & Scoring', completed: progress >= 90, icon: Target },
    { name: 'Results Ready', completed: progress >= 100, icon: CheckCircle }
  ];

  const agents = [
    { name: 'Recruiter Agent', icon: Users, color: 'blue', description: 'Technical skills analysis' },
    { name: 'Analyst Agent', icon: Target, color: 'emerald', description: 'Job requirement matching' },
    { name: 'HR Agent', icon: Shield, color: 'purple', description: 'Communication assessment' },
    { name: 'Recommender Agent', icon: Award, color: 'orange', description: 'Final ranking logic' }
  ];

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="text-center max-w-md bg-white rounded-2xl shadow-lg p-8">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-semibold text-slate-900 mb-4">Analysis Error</h1>
          <p className="text-slate-600 mb-6">{error}</p>
          <button
            onClick={() => navigate('/upload')}
            className="px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200"
          >
            Start Over
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-4">
      <div className="max-w-5xl w-full">
        {/* Main Card */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-6">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6 relative">
              <Brain className="w-10 h-10 text-blue-600 animate-pulse" />
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center">
                <Zap className="w-3 h-3 text-white" />
              </div>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-slate-900 mb-3">
              AI Multi-Agent Analysis
            </h1>
            <p className="text-lg text-slate-600 mb-2">
              Processing {totalFiles} resume{totalFiles !== 1 ? 's' : ''} with enterprise-grade AI
            </p>
            <p className="text-sm text-blue-600 font-medium">
              LangChain • RAG • Multi-Agent Orchestration
            </p>
          </div>

          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-3">
              <span className="text-sm font-medium text-slate-700">{currentStep}</span>
              <span className="text-sm font-medium text-blue-600">{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-slate-200 rounded-full h-4 overflow-hidden">
              <div 
                className="h-4 bg-gradient-to-r from-blue-500 via-purple-500 to-emerald-500 rounded-full transition-all duration-1000 ease-out relative"
                style={{ width: `${progress}%` }}
              >
                <div className="absolute inset-0 bg-white opacity-30 animate-pulse"></div>
              </div>
            </div>
          </div>

          {/* Current Agent Indicator */}
          {currentAgent && progress > 30 && progress < 70 && (
            <div className="mb-8 text-center">
              <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-50 to-purple-50 rounded-full border border-blue-200">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse mr-3"></div>
                <span className="text-sm font-medium text-blue-700">
                  {currentAgent} is analyzing candidates...
                </span>
              </div>
            </div>
          )}

          {/* Progress Steps */}
          <div className="mb-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {steps.map((step, index) => {
                const Icon = step.icon;
                return (
                  <div key={index} className="text-center">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 transition-all duration-500 ${
                      step.completed 
                        ? 'bg-emerald-100 text-emerald-600 scale-110' 
                        : progress > index * 25 
                        ? 'bg-blue-100 text-blue-600 animate-pulse' 
                        : 'bg-slate-100 text-slate-400'
                    }`}>
                      <Icon className="w-6 h-6" />
                    </div>
                    <span className={`text-xs font-medium ${
                      step.completed ? 'text-emerald-600' : 'text-slate-500'
                    }`}>
                      {step.name}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div className="bg-slate-50 rounded-xl p-4 text-center">
              <div className="text-2xl font-bold text-slate-900 mb-1">
                {processedCount}
              </div>
              <div className="text-sm text-slate-600">Files Processed</div>
            </div>
            <div className="bg-blue-50 rounded-xl p-4 text-center">
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {state.topN}
              </div>
              <div className="text-sm text-blue-700">Top Candidates</div>
            </div>
            <div className="bg-purple-50 rounded-xl p-4 text-center">
              <div className="text-2xl font-bold text-purple-600 mb-1">
                4
              </div>
              <div className="text-sm text-purple-700">AI Agents</div>
            </div>
            <div className="bg-emerald-50 rounded-xl p-4 text-center">
              <div className="text-2xl font-bold text-emerald-600 mb-1">
                {Math.round(progress)}%
              </div>
              <div className="text-sm text-emerald-700">Complete</div>
            </div>
          </div>

          {/* Current Activity */}
          <div className="text-center">
            <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-full border border-blue-200">
              <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse mr-3"></div>
              <span className="text-sm font-medium text-blue-700">
                {isAnalyzing ? 'AI agents are working...' : 'Analysis complete!'}
              </span>
            </div>
          </div>
        </div>

        {/* Agent Status Cards */}
        <div className="grid md:grid-cols-2 gap-4 mb-6">
          {agents.map((agent, index) => {
            const Icon = agent.icon;
            const isActive = currentAgent.includes(agent.name.split(' ')[0]);
            const isCompleted = progress > 70;
            
            return (
              <div key={index} className={`bg-white rounded-xl shadow-lg p-4 border-2 transition-all duration-300 ${
                isActive ? 'border-blue-300 bg-blue-50' : 
                isCompleted ? 'border-emerald-300 bg-emerald-50' : 
                'border-slate-200'
              }`}>
                <div className="flex items-center space-x-3">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    isActive ? 'bg-blue-100 text-blue-600' :
                    isCompleted ? 'bg-emerald-100 text-emerald-600' :
                    'bg-slate-100 text-slate-400'
                  }`}>
                    {isCompleted ? <CheckCircle className="w-5 h-5" /> : <Icon className="w-5 h-5" />}
                  </div>
                  <div>
                    <div className={`font-medium ${
                      isActive ? 'text-blue-900' :
                      isCompleted ? 'text-emerald-900' :
                      'text-slate-700'
                    }`}>
                      {agent.name}
                    </div>
                    <div className={`text-xs ${
                      isActive ? 'text-blue-600' :
                      isCompleted ? 'text-emerald-600' :
                      'text-slate-500'
                    }`}>
                      {agent.description}
                    </div>
                  </div>
                  {isActive && (
                    <div className="ml-auto">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-ping"></div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Extraction Details Log */}
        {extractionDetails.length > 0 && (
          <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
            <h3 className="font-semibold text-slate-900 mb-4">📋 Processing Log</h3>
            <div className="max-h-40 overflow-y-auto space-y-1">
              {extractionDetails.slice(-10).map((detail, index) => (
                <div key={index} className="text-xs text-slate-600 font-mono bg-slate-50 p-2 rounded">
                  {detail}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Technology Stack Info */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className="font-semibold text-slate-900 mb-4 text-center">🚀 Technology Stack in Action</h3>
          <div className="grid md:grid-cols-3 gap-4 text-sm">
            <div className="text-center p-3 bg-slate-50 rounded-lg">
              <div className="font-medium text-slate-900 mb-1">LangChain RAG</div>
              <div className="text-slate-600">Document processing & content extraction</div>
            </div>
            <div className="text-center p-3 bg-slate-50 rounded-lg">
              <div className="font-medium text-slate-900 mb-1">Gemini Pro API</div>
              <div className="text-slate-600">Multi-agent AI analysis & scoring</div>
            </div>
            <div className="text-center p-3 bg-slate-50 rounded-lg">
              <div className="font-medium text-slate-900 mb-1">TypeScript</div>
              <div className="text-slate-600">Type-safe enterprise architecture</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExtractionPage;