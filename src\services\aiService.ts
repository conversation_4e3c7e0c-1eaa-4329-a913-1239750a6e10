import { GoogleGenerativeAI } from '@google/generative-ai';
import { ExtractedData, Scores } from '../utils/metadataExtractor';

// Move API key to environment variable in production
const API_KEY = 'AIzaSyC7tDslmmvyhlaHvcueynahnZod4OJgArA';
const genAI = new GoogleGenerativeAI(API_KEY);

export interface ResumeForAnalysis {
  id: string;
  extractedData: ExtractedData;
  scores: Scores;
  file: File;
}

export interface AIFeedback {
  id: string;
  analysis: {
    recruiterAnalysis: string;
    analystAnalysis: string;
    hrAnalysis: string;
    recommendation: string;
  };
}

export const getFeedbackForTopN = async (topResumes: ResumeForAnalysis[]): Promise<AIFeedback[]> => {
  const model = genAI.getGenerativeModel({ model: 'gemini-pro' });

  try {
    // Process in batches to avoid rate limits and improve performance
    const batchSize = 3;
    const feedbacks: AIFeedback[] = [];
    
    for (let i = 0; i < topResumes.length; i += batchSize) {
      const batch = topResumes.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (resume) => {
        const prompt = createAnalysisPrompt(resume);
        
        try {
          const result = await model.generateContent(prompt);
          const text = result.response.text();
          
          return {
            id: resume.id,
            analysis: parseAIResponse(text)
          };
        } catch (error) {
          console.error(`Error generating feedback for resume ${resume.id}:`, error);
          return {
            id: resume.id,
            analysis: {
              recruiterAnalysis: 'Analysis temporarily unavailable due to processing error.',
              analystAnalysis: 'Analysis temporarily unavailable due to processing error.',
              hrAnalysis: 'Analysis temporarily unavailable due to processing error.',
              recommendation: 'Unable to generate recommendation at this time.'
            }
          };
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      feedbacks.push(...batchResults);
      
      // Small delay between batches to respect rate limits
      if (i + batchSize < topResumes.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    return feedbacks;
  } catch (error) {
    console.error('Error in getFeedbackForTopN:', error);
    // Return default feedback for all resumes
    return topResumes.map(resume => ({
      id: resume.id,
      analysis: {
        recruiterAnalysis: 'Technical analysis unavailable - please try again.',
        analystAnalysis: 'Job fit analysis unavailable - please try again.',
        hrAnalysis: 'Communication assessment unavailable - please try again.',
        recommendation: 'Overall recommendation unavailable - please try again.'
      }
    }));
  }
};

function createAnalysisPrompt(resume: ResumeForAnalysis): string {
  return `As an AI recruitment system with three specialized agents, analyze this candidate:

CANDIDATE PROFILE:
Name: ${resume.extractedData.name}
Email: ${resume.extractedData.email}
Skills: ${resume.extractedData.skills.slice(0, 10).join(', ')}${resume.extractedData.skills.length > 10 ? ` (+${resume.extractedData.skills.length - 10} more)` : ''}
Education: ${resume.extractedData.education.join('; ') || 'Not specified'}
Experience: ${resume.extractedData.experience.slice(0, 3).join('; ') || 'Not specified'}
Summary: ${resume.extractedData.summary || 'Not provided'}
Projects: ${resume.extractedData.projects.slice(0, 2).join('; ') || 'None listed'}
Certifications: ${resume.extractedData.certifications.join(', ') || 'None listed'}

SCORES ACHIEVED:
• Technical Skills (Recruiter): ${resume.scores.recruiterScore}%
• Job Match (Analyst): ${resume.scores.analystScore}%
• Communication (HR): ${resume.scores.hrScore}%
• Final Score: ${resume.scores.finalScore}%

Provide analysis from three perspectives:

RECRUITER AGENT - Focus on technical capabilities and skill relevance:
[Analyze technical skills, experience quality, and technical fit]

ANALYST AGENT - Focus on job requirements matching and potential:
[Analyze job fit, growth potential, and role alignment]

HR AGENT - Focus on soft skills, communication, and cultural fit:
[Analyze communication skills, professionalism, and team fit potential]

RECOMMENDATION - Overall hiring recommendation:
[Provide clear recommendation with key strengths and areas of concern]

Keep each analysis concise (2-3 sentences) and actionable.`;
}

function parseAIResponse(text: string): {
  recruiterAnalysis: string;
  analystAnalysis: string;
  hrAnalysis: string;
  recommendation: string;
} {
  try {
    // Split by agent sections
    const sections = text.split(/(?:RECRUITER AGENT|ANALYST AGENT|HR AGENT|RECOMMENDATION)/i);
    
    if (sections.length >= 5) {
      return {
        recruiterAnalysis: cleanAnalysisText(sections[1]),
        analystAnalysis: cleanAnalysisText(sections[2]),
        hrAnalysis: cleanAnalysisText(sections[3]),
        recommendation: cleanAnalysisText(sections[4])
      };
    }
    
    // Fallback: split by paragraphs
    const paragraphs = text.split('\n\n').filter(p => p.trim().length > 0);
    
    return {
      recruiterAnalysis: paragraphs[0] || 'Technical skills assessment completed.',
      analystAnalysis: paragraphs[1] || 'Job matching analysis completed.',
      hrAnalysis: paragraphs[2] || 'HR evaluation completed.',
      recommendation: paragraphs[3] || 'Candidate evaluation completed.'
    };
  } catch (error) {
    return {
      recruiterAnalysis: 'Technical analysis completed successfully.',
      analystAnalysis: 'Job fit analysis shows good potential.',
      hrAnalysis: 'Communication skills appear adequate.',
      recommendation: 'Consider for further evaluation based on specific role requirements.'
    };
  }
}

function cleanAnalysisText(text: string): string {
  return text
    .replace(/^\s*[-•*]\s*/, '') // Remove bullet points
    .replace(/^\s*\[.*?\]\s*/, '') // Remove bracketed instructions
    .trim()
    .substring(0, 300) // Limit length
    || 'Analysis completed.'; // Fallback
}