import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Upload, FileText, BarChart3, Brain, Users, Award, Zap, Target, Shield, TrendingUp, CheckCircle } from 'lucide-react';

const HomePage: React.FC = () => {
  const agents = [
    {
      icon: Users,
      name: 'Recruiter Agent',
      description: 'Extracts education, skills, tools, and experience from resumes with advanced NLP techniques.',
      capabilities: ['Skills Extraction', 'Experience Analysis', 'Education Parsing', 'Certification Detection'],
      color: 'blue'
    },
    {
      icon: Target,
      name: 'Analyst Agent',
      description: 'Matches extracted features to job descriptions using sophisticated scoring algorithms.',
      capabilities: ['Job Matching', 'Skill Alignment', 'Experience Relevance', 'Project Similarity'],
      color: 'emerald'
    },
    {
      icon: Shield,
      name: 'HR Agent',
      description: 'Evaluates communication tone, soft skills, and identifies potential red flags.',
      capabilities: ['Communication Assessment', 'Soft Skills Analysis', 'Red Flag Detection', 'Cultural Fit'],
      color: 'purple'
    },
    {
      icon: Award,
      name: 'Recommender Agent',
      description: 'Ranks resumes and provides top 3 recommendations with detailed explanations.',
      capabilities: ['Intelligent Ranking', 'Score Aggregation', 'Recommendation Logic', 'Detailed Insights'],
      color: 'orange'
    }
  ];

  const features = [
    {
      icon: Brain,
      title: 'AI-Powered Multi-Agent System',
      description: 'Four specialized AI agents work together using LangGraph orchestration for comprehensive resume analysis.',
    },
    {
      icon: Zap,
      title: 'LangChain & RAG Integration',
      description: 'Advanced document processing with Retrieval-Augmented Generation for accurate content extraction.',
    },
    {
      icon: TrendingUp,
      title: 'Real-time Analysis',
      description: 'Fast, efficient processing with optimized agent execution and real-time progress tracking.',
    },
  ];

  const steps = [
    {
      icon: Upload,
      title: 'Upload Resumes',
      description: 'Upload multiple PDF/DOCX resume files (supports 50+ resumes simultaneously).',
      link: '/upload',
    },
    {
      icon: FileText,
      title: 'Job Description',
      description: 'Define job requirements, skills, experience, and role specifications.',
      link: '/job-description',
    },
    {
      icon: BarChart3,
      title: 'AI Analysis & Results',
      description: 'Get ranked candidates with detailed multi-agent analysis and downloadable reports.',
      link: '/results',
    },
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-600 border-blue-200',
      emerald: 'bg-emerald-100 text-emerald-600 border-emerald-200',
      purple: 'bg-purple-100 text-purple-600 border-purple-200',
      orange: 'bg-orange-100 text-orange-600 border-orange-200'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
          <div className="absolute top-40 right-20 w-72 h-72 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
          <div className="absolute -bottom-8 left-40 w-72 h-72 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="relative">
                <Brain className="w-16 h-16 text-blue-200" />
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-400 rounded-full flex items-center justify-center">
                  <Zap className="w-3 h-3 text-white" />
                </div>
              </div>
            </div>
            
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              AI-Powered Multi-Agent
              <span className="block text-blue-200">Resume Screener</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-4xl mx-auto leading-relaxed">
              Enterprise-grade resume screening with LangChain, RAG, and intelligent multi-agent orchestration. 
              Screen 50+ resumes instantly with production-quality AI analysis.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/upload"
                className="inline-flex items-center px-8 py-4 bg-white text-blue-700 font-semibold rounded-xl hover:bg-blue-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                Start Screening Resumes
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
              
              <Link
                to="/job-description"
                className="inline-flex items-center px-8 py-4 bg-transparent border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-blue-700 transition-all duration-200"
              >
                Define Job Requirements
                <FileText className="w-5 h-5 ml-2" />
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Multi-Agent System Section */}
      <div className="py-24 bg-slate-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Meet Our AI Agent Team
            </h2>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto">
              Four specialized AI agents powered by Google's Gemini API work together using LangGraph orchestration 
              to provide comprehensive resume analysis and intelligent candidate ranking.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {agents.map((agent, index) => {
              const Icon = agent.icon;
              return (
                <div key={index} className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-slate-100">
                  <div className="p-6">
                    <div className={`w-16 h-16 rounded-full flex items-center justify-center mb-6 border-2 ${getColorClasses(agent.color)}`}>
                      <Icon className="w-8 h-8" />
                    </div>
                    
                    <h3 className="text-xl font-bold text-slate-900 mb-3">{agent.name}</h3>
                    <p className="text-slate-600 mb-4 text-sm leading-relaxed">{agent.description}</p>
                    
                    <div className="space-y-2">
                      {agent.capabilities.map((capability, capIndex) => (
                        <div key={capIndex} className="flex items-center text-sm text-slate-700">
                          <CheckCircle className="w-4 h-4 text-emerald-500 mr-2 flex-shrink-0" />
                          <span>{capability}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Enterprise-Grade Technology Stack
            </h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Built with cutting-edge AI technologies for production-ready performance and scalability.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="text-center p-8 rounded-2xl bg-gradient-to-br from-slate-50 to-blue-50 hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 border border-slate-100">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Icon className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-4">{feature.title}</h3>
                  <p className="text-slate-600 leading-relaxed">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Process Section */}
      <div className="py-24 bg-slate-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              How It Works
            </h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Three simple steps to get comprehensive AI-powered resume analysis and candidate rankings.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {steps.map((step, index) => {
              const Icon = step.icon;
              return (
                <div key={index} className="relative">
                  <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-100">
                    <div className="flex items-center justify-between mb-6">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Icon className="w-6 h-6 text-blue-600" />
                      </div>
                      <div className="text-2xl font-bold text-blue-600">0{index + 1}</div>
                    </div>
                    
                    <h3 className="text-xl font-bold text-slate-900 mb-4">{step.title}</h3>
                    <p className="text-slate-600 mb-6 leading-relaxed">{step.description}</p>
                    
                    <Link
                      to={step.link}
                      className="inline-flex items-center text-blue-600 font-medium hover:text-blue-700 transition-colors duration-200 group"
                    >
                      Get Started
                      <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-200" />
                    </Link>
                  </div>
                  
                  {index < steps.length - 1 && (
                    <div className="hidden md:block absolute top-1/2 -right-4 transform -translate-y-1/2 z-10">
                      <ArrowRight className="w-8 h-8 text-slate-300" />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Technical Specifications */}
      <div className="py-16 bg-white border-t border-slate-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl font-bold text-slate-900 mb-4">Technical Specifications</h2>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <h3 className="font-semibold text-slate-900">AI & ML Technologies</h3>
              <ul className="space-y-2 text-sm text-slate-600">
                <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-500 mr-2" />Google Gemini API Integration</li>
                <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-500 mr-2" />LangChain Document Processing</li>
                <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-500 mr-2" />RAG (Retrieval-Augmented Generation)</li>
                <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-500 mr-2" />LangGraph Multi-Agent Orchestration</li>
              </ul>
            </div>
            
            <div className="space-y-4">
              <h3 className="font-semibold text-slate-900">Frontend & Performance</h3>
              <ul className="space-y-2 text-sm text-slate-600">
                <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-500 mr-2" />TypeScript + React + Vite</li>
                <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-500 mr-2" />Responsive Enterprise UI</li>
                <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-500 mr-2" />Real-time Progress Tracking</li>
                <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-500 mr-2" />Optimized Agent Execution</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-16 bg-gradient-to-r from-blue-600 to-indigo-700">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Transform Your Hiring Process?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Experience the power of AI-driven resume screening with our multi-agent system.
          </p>
          <Link
            to="/upload"
            className="inline-flex items-center px-8 py-4 bg-white text-blue-700 font-semibold rounded-xl hover:bg-blue-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
          >
            Start Screening Now
            <ArrowRight className="w-5 h-5 ml-2" />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HomePage;