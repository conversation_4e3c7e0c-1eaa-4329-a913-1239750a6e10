import * as pdfjsLib from 'pdfjs-dist';
import mammoth from 'mammoth';
import { GoogleGenerativeAI } from '@google/generative-ai';

pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/4.0.379/pdf.worker.min.js`;

const API_KEY = 'AIzaSyC7tDslmmvyhlaHvcueynahnZod4OJgArA';
const genAI = new GoogleGenerativeAI(API_KEY);

export interface DocumentChunk {
  content: string;
  metadata: {
    fileName: string;
    pageNumber?: number;
    chunkIndex: number;
  };
}

export interface WorkExperience {
  position: string;
  company: string;
  duration: string;
  description: string;
  technologies?: string[];
}

export interface ExtractedData {
  name: string;
  email: string;
  phone: string;
  skills: string[];
  education: string[];
  experience: string[];
  summary: string;
  certifications: string[];
  projects: string[];
  workExperience: WorkExperience[];
  totalExperienceYears: number;
  locations?: string[];
  languages?: string[];
  linkedinUrl?: string;
  githubUrl?: string;
}

export class LangChainRAGService {
  private model = genAI.getGenerativeModel({ 
    model: 'gemini-pro',
    generationConfig: {
      temperature: 0.1, // Lower temperature for more consistent extraction
      topP: 0.8,
      topK: 40,
      maxOutputTokens: 3000, // Increased for better extraction
    }
  });

  async extractDocumentContent(file: File): Promise<string> {
    try {
      console.log(`Starting extraction for: ${file.name}`);
      const extension = file.name.split('.').pop()?.toLowerCase();

      let content = '';
      if (extension === 'pdf') {
        content = await this.extractPDFContent(file);
      } else if (extension === 'docx' || extension === 'doc') {
        content = await this.extractDocxContent(file);
      } else if (extension === 'txt') {
        content = await this.extractTextContent(file);
      } else {
        throw new Error(`Unsupported file format: ${extension}`);
      }

      console.log(`Extracted ${content.length} characters from ${file.name}`);
      return content;
    } catch (error) {
      console.error(`Error extracting content from ${file.name}:`, error);
      throw error;
    }
  }

  private async extractPDFContent(file: File): Promise<string> {
    const arrayBuffer = await file.arrayBuffer();
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    const textPromises = [];
    
    for (let i = 1; i <= pdf.numPages; i++) {
      textPromises.push(
        pdf.getPage(i).then(async (page) => {
          const textContent = await page.getTextContent();
          return textContent.items.map((item: any) => item.str).join(' ').replace(/\s+/g, ' ');
        })
      );
    }
    
    const pages = await Promise.all(textPromises);
    return pages.join('\n\n').trim();
  }

  private async extractDocxContent(file: File): Promise<string> {
    const buffer = await file.arrayBuffer();
    const result = await mammoth.extractRawText({ arrayBuffer: buffer });
    return result.value.replace(/\s+/g, ' ').trim();
  }

  private async extractTextContent(file: File): Promise<string> {
    return await file.text();
  }

  async extractStructuredData(content: string, fileName: string): Promise<ExtractedData> {
    try {
      console.log(`Starting enhanced AI extraction for ${fileName} with ${content.length} characters`);
      
      if (!content || content.trim().length < 50) {
        console.warn(`Content too short for ${fileName}, using enhanced fallback`);
        return this.extractWithEnhancedRules(content, fileName);
      }

      // Use enhanced AI extraction with better prompts
      const aiExtracted = await this.extractWithEnhancedAI(content, fileName);
      if (aiExtracted) {
        console.log(`Enhanced AI extraction successful for ${fileName}`);
        return aiExtracted;
      }
      
      console.log(`AI extraction failed for ${fileName}, using enhanced rule-based fallback`);
      return this.extractWithEnhancedRules(content, fileName);
    } catch (error) {
      console.error(`Structured data extraction failed for ${fileName}:`, error);
      return this.extractWithEnhancedRules(content, fileName);
    }
  }

  private async extractWithEnhancedAI(content: string, fileName: string): Promise<ExtractedData | null> {
    try {
      // Truncate content if too long to avoid token limits
      const truncatedContent = content.length > 8000 ? content.substring(0, 8000) + '...' : content;
      
      const prompt = `
        You are an expert resume parser and ATS system. Extract ALL information from this resume with high accuracy.
        Focus on finding EVERY skill, technology, tool, framework, and keyword that could be relevant for job matching.
        
        RESUME CONTENT:
        ${truncatedContent}
        
        EXTRACTION REQUIREMENTS:
        1. Extract EVERY technical skill, programming language, framework, tool, software, and technology mentioned
        2. Include both hard skills (technical) and soft skills (leadership, communication, etc.)
        3. Extract ALL work experience with detailed company names, positions, durations, and descriptions
        4. Find ALL educational qualifications, certifications, and courses
        5. Extract ALL projects with technologies used
        6. Find contact information (email, phone, LinkedIn, GitHub, location)
        7. Calculate accurate years of experience from work history
        8. Extract professional summary/objective
        
        KEYWORD EXTRACTION FOCUS:
        - Programming languages: JavaScript, Python, Java, C++, C#, PHP, Ruby, Go, Rust, Swift, Kotlin, etc.
        - Frameworks: React, Angular, Vue, Node.js, Django, Flask, Spring, Laravel, Express, etc.
        - Databases: MySQL, PostgreSQL, MongoDB, Redis, Oracle, SQL Server, etc.
        - Cloud platforms: AWS, Azure, GCP, Heroku, Docker, Kubernetes, etc.
        - Tools: Git, Jenkins, Docker, Kubernetes, Jira, Confluence, etc.
        - Methodologies: Agile, Scrum, DevOps, CI/CD, TDD, etc.
        - Soft skills: Leadership, Communication, Problem-solving, Team collaboration, etc.
        
        Return information in this EXACT JSON format (no additional text):
        {
          "name": "Full Name Here",
          "email": "<EMAIL>",
          "phone": "+1234567890",
          "skills": ["JavaScript", "Python", "React", "Node.js", "AWS", "Leadership", "Communication"],
          "education": ["Bachelor of Computer Science, XYZ University, 2020", "Master of Technology, ABC University, 2022"],
          "experience": ["Software Engineer at TechCorp (2020-2023)", "Senior Developer at StartupXYZ (2018-2020)"],
          "summary": "Experienced software engineer with 5+ years in full-stack development...",
          "certifications": ["AWS Certified Developer", "Google Cloud Professional", "Scrum Master Certified"],
          "projects": ["E-commerce Platform - Built using React, Node.js, MongoDB", "Mobile App - Developed iOS app using Swift and Firebase"],
          "workExperience": [
            {
              "position": "Senior Software Engineer",
              "company": "TechCorp",
              "duration": "2020-2023",
              "description": "Led development of scalable web applications using React, Node.js, and AWS. Managed team of 5 developers and implemented CI/CD pipelines.",
              "technologies": ["React", "Node.js", "AWS", "Docker", "Jenkins"]
            },
            {
              "position": "Software Developer",
              "company": "StartupXYZ",
              "duration": "2018-2020",
              "description": "Developed full-stack applications using Python, Django, and PostgreSQL. Collaborated with cross-functional teams.",
              "technologies": ["Python", "Django", "PostgreSQL", "JavaScript"]
            }
          ],
          "locations": ["San Francisco, CA", "Remote"],
          "languages": ["English", "Spanish", "French"],
          "linkedinUrl": "linkedin.com/in/username",
          "githubUrl": "github.com/username"
        }
        
        CRITICAL REQUIREMENTS:
        1. BE EXTREMELY THOROUGH - Don't miss any skills or technologies
        2. Extract EVERY skill mentioned, even if it appears only once
        3. Include variations of skills (e.g., JS and JavaScript, ML and Machine Learning)
        4. For work experience, include ALL technologies mentioned in job descriptions
        5. Calculate experience years by parsing ALL date ranges mentioned
        6. Use "N/A" for truly missing information, empty arrays [] for missing lists
        7. Return ONLY the JSON object, no other text
        8. Ensure all skills are properly capitalized (e.g., "JavaScript" not "javascript")
      `;

      const result = await this.model.generateContent(prompt);
      const response = result.response.text().trim();
      
      console.log(`Enhanced AI response for ${fileName}:`, response.substring(0, 300) + '...');
      
      // Extract JSON from response
      let jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        // Try to find JSON in code blocks
        jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/) || response.match(/```\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonMatch[0] = jsonMatch[1];
        }
      }
      
      if (jsonMatch) {
        try {
          const parsed = JSON.parse(jsonMatch[0]);
          
          // Enhanced validation and cleaning
          const cleanedData = this.enhancedValidateAndCleanExtractedData(parsed, fileName);
          
          // Calculate total experience years with better logic
          const totalExperienceYears = this.calculateEnhancedExperienceYears(cleanedData.workExperience || [], cleanedData.experience || []);
          
          const finalData = {
            ...cleanedData,
            totalExperienceYears,
            workExperience: cleanedData.workExperience || []
          };
          
          console.log(`Successfully extracted enhanced data for ${fileName}:`, {
            name: finalData.name,
            skillsCount: finalData.skills.length,
            experienceYears: finalData.totalExperienceYears,
            workExperienceCount: finalData.workExperience.length,
            projectsCount: finalData.projects.length,
            certificationsCount: finalData.certifications.length
          });
          
          return finalData;
        } catch (parseError) {
          console.error(`JSON parsing error for ${fileName}:`, parseError);
          return null;
        }
      }
      
      console.warn(`No valid JSON found in AI response for ${fileName}`);
      return null;
    } catch (error) {
      console.error(`Enhanced AI extraction failed for ${fileName}:`, error);
      return null;
    }
  }

  private enhancedValidateAndCleanExtractedData(data: any, fileName: string): ExtractedData {
    // Enhanced skill processing
    const skills = Array.isArray(data.skills) ? data.skills : [];
    const additionalSkills = this.extractAdditionalSkills(data);
    const allSkills = [...new Set([...skills, ...additionalSkills])];
    
    // Clean and standardize skills
    const cleanedSkills = allSkills
      .filter(skill => skill && typeof skill === 'string')
      .map(skill => this.standardizeSkill(skill.trim()))
      .filter(skill => skill.length > 1 && skill.length < 50)
      .filter(skill => !this.isCommonWord(skill));

    return {
      name: (data.name && data.name !== 'N/A') ? data.name : fileName.replace(/\.[^/.]+$/, ''),
      email: data.email || 'N/A',
      phone: data.phone || 'N/A',
      skills: [...new Set(cleanedSkills)], // Remove duplicates
      education: Array.isArray(data.education) ? data.education.filter(Boolean) : [],
      experience: Array.isArray(data.experience) ? data.experience.filter(Boolean) : [],
      summary: data.summary || '',
      certifications: Array.isArray(data.certifications) ? data.certifications.filter(Boolean) : [],
      projects: Array.isArray(data.projects) ? data.projects.filter(Boolean) : [],
      workExperience: Array.isArray(data.workExperience) ? data.workExperience.map((exp: any) => ({
        position: exp.position || 'N/A',
        company: exp.company || 'N/A',
        duration: exp.duration || 'N/A',
        description: exp.description || '',
        technologies: Array.isArray(exp.technologies) ? exp.technologies.map(tech => this.standardizeSkill(tech)) : []
      })) : [],
      totalExperienceYears: 0, // Will be calculated later
      locations: Array.isArray(data.locations) ? data.locations.filter(Boolean) : [],
      languages: Array.isArray(data.languages) ? data.languages.filter(Boolean) : [],
      linkedinUrl: data.linkedinUrl || undefined,
      githubUrl: data.githubUrl || undefined
    };
  }

  private extractAdditionalSkills(data: any): string[] {
    const additionalSkills: string[] = [];
    
    // Extract skills from work experience technologies
    if (Array.isArray(data.workExperience)) {
      data.workExperience.forEach((exp: any) => {
        if (Array.isArray(exp.technologies)) {
          additionalSkills.push(...exp.technologies);
        }
      });
    }
    
    // Extract skills from projects
    if (Array.isArray(data.projects)) {
      data.projects.forEach((project: string) => {
        const projectSkills = this.extractSkillsFromText(project);
        additionalSkills.push(...projectSkills);
      });
    }
    
    // Extract skills from summary
    if (data.summary) {
      const summarySkills = this.extractSkillsFromText(data.summary);
      additionalSkills.push(...summarySkills);
    }
    
    return additionalSkills;
  }

  private extractSkillsFromText(text: string): string[] {
    const commonSkills = [
      'JavaScript', 'Python', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'Swift', 'Kotlin',
      'React', 'Angular', 'Vue', 'Node.js', 'Django', 'Flask', 'Spring', 'Laravel', 'Express',
      'MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'Oracle', 'SQL Server', 'SQLite',
      'AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'Jenkins', 'Git', 'Jira', 'Confluence',
      'HTML', 'CSS', 'Sass', 'Less', 'Bootstrap', 'Tailwind', 'jQuery', 'TypeScript',
      'Machine Learning', 'AI', 'Deep Learning', 'TensorFlow', 'PyTorch', 'Scikit-learn',
      'Agile', 'Scrum', 'DevOps', 'CI/CD', 'TDD', 'REST', 'GraphQL', 'API'
    ];
    
    const foundSkills: string[] = [];
    const lowerText = text.toLowerCase();
    
    commonSkills.forEach(skill => {
      if (lowerText.includes(skill.toLowerCase())) {
        foundSkills.push(skill);
      }
    });
    
    return foundSkills;
  }

  private standardizeSkill(skill: string): string {
    const skillMap: { [key: string]: string } = {
      'js': 'JavaScript',
      'javascript': 'JavaScript',
      'ts': 'TypeScript',
      'typescript': 'TypeScript',
      'py': 'Python',
      'python': 'Python',
      'node': 'Node.js',
      'nodejs': 'Node.js',
      'react': 'React',
      'reactjs': 'React',
      'angular': 'Angular',
      'angularjs': 'Angular',
      'vue': 'Vue.js',
      'vuejs': 'Vue.js',
      'ml': 'Machine Learning',
      'ai': 'Artificial Intelligence',
      'aws': 'AWS',
      'gcp': 'Google Cloud Platform',
      'azure': 'Microsoft Azure'
    };
    
    const lowerSkill = skill.toLowerCase();
    return skillMap[lowerSkill] || skill;
  }

  private isCommonWord(word: string): boolean {
    const commonWords = ['and', 'or', 'the', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
    return commonWords.includes(word.toLowerCase());
  }

  private extractWithEnhancedRules(content: string, fileName: string): ExtractedData {
    console.log(`Using enhanced rule-based extraction for ${fileName}`);
    
    // Normalize content for easier parsing
    const normalized = content.replace(/\r\n/g, '\n').replace(/\n{2,}/g, '\n\n');

    // Enhanced name extraction with multiple patterns
    let name = '';
    const namePatterns = [
      /^\s*Name[:\-]?\s*(.+)$/im,
      /^\s*([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s*$/m,
      /^([A-Z][A-Z\s]+)$/m,
      /Resume of\s+([A-Za-z\s]+)/i,
      /CV of\s+([A-Za-z\s]+)/i
    ];

    for (const pattern of namePatterns) {
      const match = normalized.match(pattern);
      if (match) {
        name = match[1].trim();
        break;
      }
    }

    if (!name) {
      const lines = normalized.split('\n').map(l => l.trim()).filter(Boolean);
      for (const line of lines.slice(0, 10)) {
        if (
          /^[A-Za-z]+ [A-Za-z]+/.test(line) &&
          !line.toLowerCase().includes('curriculum') &&
          !line.toLowerCase().includes('resume') &&
          !line.match(/@|\d{3,}/) &&
          line.length < 50 &&
          line.length > 5
        ) {
          name = line;
          break;
        }
      }
    }

    if (!name) name = fileName.replace(/\.[^/.]+$/, '');

    // Enhanced email extraction
    const emailPatterns = [
      /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
      /email[:\s]+([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/gi
    ];
    let email = 'N/A';
    for (const pattern of emailPatterns) {
      const matches = normalized.match(pattern);
      if (matches) {
        email = matches[0].includes('@') ? matches[0] : matches[0].split(/[:\s]+/)[1];
        break;
      }
    }

    // Enhanced phone extraction
    const phonePatterns = [
      /(?:phone|tel|mobile|cell)[:\s]*(\+?\d{1,3}[-.\s]?(?:\(?\d{2,4}\)?[-.\s]?)?\d{3,4}[-.\s]?\d{3,4})/gi,
      /(\+?\d{1,3}[-.\s]?)?(\(?\d{2,4}\)?[-.\s]?)?\d{3,4}[-.\s]?\d{3,4}/g
    ];
    let phone = 'N/A';
    for (const pattern of phonePatterns) {
      const matches = normalized.match(pattern);
      if (matches) {
        phone = matches[0];
        break;
      }
    }

    // Enhanced skills extraction with comprehensive patterns
    const skillsKeywords = [
      'skills', 'technical skills', 'technologies', 'programming languages', 'tools', 'software',
      'frameworks', 'libraries', 'platforms', 'languages', 'expertise', 'proficient', 'experienced'
    ];
    
    let skills: string[] = [];
    
    // Try each keyword pattern
    for (const keyword of skillsKeywords) {
      const pattern = new RegExp(`(?:${keyword})[:\\-]?\\s*([^\\n]+(?:\\n[^\\n]+)*)`, 'i');
      const match = normalized.match(pattern);
      if (match) {
        const skillText = match[1];
        const extractedSkills = this.parseSkillsFromText(skillText);
        skills = [...skills, ...extractedSkills];
      }
    }

    // Extract skills from entire content using common technology keywords
    const contentSkills = this.extractSkillsFromText(normalized);
    skills = [...skills, ...contentSkills];

    // Remove duplicates, clean, and standardize
    skills = [...new Set(skills)]
      .map(skill => this.standardizeSkill(skill.replace(/^[•\-\*]\s*/, '').trim()))
      .filter(skill => skill && skill.length > 1 && skill.length < 50)
      .filter(skill => !this.isCommonWord(skill));

    // Enhanced education extraction
    const educationPatterns = [
      /(?:education|academic|qualifications?|degree)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
      /(?:bachelor|master|phd|doctorate|diploma)[^\n]*/gi,
      /(?:university|college|institute|school)[^\n]*/gi
    ];
    let education: string[] = [];
    for (const pattern of educationPatterns) {
      const matches = normalized.match(pattern);
      if (matches) {
        const eduText = Array.isArray(matches) ? matches.join('\n') : matches;
        education = [...education, ...eduText.split(/\n|•|\-/)
          .map(e => e.trim())
          .filter(e => e && e.length > 10)
          .map(e => e.replace(/^[•\-\*]\s*/, ''))];
      }
    }
    education = [...new Set(education)];

    // Enhanced work experience extraction
    const experiencePatterns = [
      /(?:experience|employment|work history|professional experience)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
      /(?:worked|employed|position|role)[^\n]*/gi
    ];
    let experience: string[] = [];
    let workExperience: WorkExperience[] = [];
    
    for (const pattern of experiencePatterns) {
      const match = normalized.match(pattern);
      if (match) {
        const experienceText = match[1] || match[0];
        const experienceBlocks = experienceText.split(/\n\s*\n/).filter(block => block.trim());
        
        for (const block of experienceBlocks) {
          const lines = block.split('\n').map(l => l.trim()).filter(Boolean);
          if (lines.length >= 1) {
            const firstLine = lines[0];
            const datePattern = /(\d{4})\s*[-–]\s*(\d{4}|present|current)/i;
            const dateMatch = block.match(datePattern);
            
            if (dateMatch) {
              const workExp: WorkExperience = {
                position: firstLine.split(' at ')[0] || firstLine,
                company: firstLine.includes(' at ') ? firstLine.split(' at ')[1] : 'N/A',
                duration: dateMatch[0],
                description: lines.slice(1).join(' '),
                technologies: this.extractSkillsFromText(block)
              };
              
              workExperience.push(workExp);
            }
            
            experience.push(block.replace(/^[•\-\*]\s*/, '').trim());
          }
        }
      }
    }

    // Enhanced summary extraction
    const summaryPatterns = [
      /(?:summary|profile|objective|about)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
      /(?:professional summary|career objective)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i
    ];
    let summary = '';
    for (const pattern of summaryPatterns) {
      const match = normalized.match(pattern);
      if (match) {
        summary = match[1].trim();
        if (summary.length > 500) {
          summary = summary.substring(0, 500) + '...';
        }
        break;
      }
    }

    // Enhanced certifications extraction
    const certsPatterns = [
      /(?:certifications?|licenses?|credentials?)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
      /(?:certified|licensed)[^\n]*/gi
    ];
    let certifications: string[] = [];
    for (const pattern of certsPatterns) {
      const matches = normalized.match(pattern);
      if (matches) {
        const certsText = Array.isArray(matches) ? matches.join('\n') : matches;
        certifications = [...certifications, ...certsText.split(/\n|•|\-/)
          .map(c => c.trim())
          .filter(c => c && c.length > 5)
          .map(c => c.replace(/^[•\-\*]\s*/, ''))];
      }
    }
    certifications = [...new Set(certifications)];

    // Enhanced projects extraction
    const projectsPatterns = [
      /(?:projects?|portfolio)[:\-]?\s*([^\n]+(?:\n[^\n]+)*)/i,
      /(?:developed|built|created)[^\n]*/gi
    ];
    let projects: string[] = [];
    for (const pattern of projectsPatterns) {
      const matches = normalized.match(pattern);
      if (matches) {
        const projectsText = Array.isArray(matches) ? matches.join('\n') : matches;
        projects = [...projects, ...projectsText.split(/\n\s*\n/)
          .map(p => p.trim())
          .filter(p => p && p.length > 15)
          .map(p => p.replace(/^[•\-\*]\s*/, ''))];
      }
    }
    projects = [...new Set(projects)];

    // Enhanced URL extraction
    const linkedinMatch = normalized.match(/linkedin\.com\/in\/[\w-]+/i);
    const githubMatch = normalized.match(/github\.com\/[\w-]+/i);
    
    const totalExperienceYears = this.calculateEnhancedExperienceYears(workExperience, experience);

    const result = {
      name,
      email,
      phone,
      skills,
      education,
      experience,
      summary,
      certifications,
      projects,
      workExperience,
      totalExperienceYears,
      locations: [],
      languages: [],
      linkedinUrl: linkedinMatch ? linkedinMatch[0] : undefined,
      githubUrl: githubMatch ? githubMatch[0] : undefined
    };

    console.log(`Enhanced rule-based extraction completed for ${fileName}:`, {
      name: result.name,
      skillsCount: result.skills.length,
      experienceYears: result.totalExperienceYears,
      workExperienceCount: result.workExperience.length,
      projectsCount: result.projects.length,
      certificationsCount: result.certifications.length
    });

    return result;
  }

  private parseSkillsFromText(text: string): string[] {
    const skills: string[] = [];
    
    // Split by common delimiters
    const delimiters = [',', '|', '•', '-', '/', '\\', ';', '\n'];
    let parts = [text];
    
    delimiters.forEach(delimiter => {
      const newParts: string[] = [];
      parts.forEach(part => {
        newParts.push(...part.split(delimiter));
      });
      parts = newParts;
    });
    
    // Clean and filter parts
    parts.forEach(part => {
      const cleaned = part.trim().replace(/^[•\-\*]\s*/, '');
      if (cleaned.length > 1 && cleaned.length < 50) {
        skills.push(cleaned);
      }
    });
    
    return skills;
  }

  private calculateEnhancedExperienceYears(workExperience: WorkExperience[], experience: string[]): number {
    let totalYears = 0;
    const currentYear = new Date().getFullYear();
    
    // Calculate from work experience
    if (workExperience && workExperience.length > 0) {
      for (const exp of workExperience) {
        if (exp.duration) {
          const years = this.parseExperienceYears(exp.duration, currentYear);
          totalYears += years;
        }
      }
    }
    
    // If no work experience, try to extract from experience strings
    if (totalYears === 0 && experience && experience.length > 0) {
      for (const exp of experience) {
        const years = this.parseExperienceYears(exp, currentYear);
        totalYears += years;
      }
    }
    
    // Cap at reasonable maximum
    return Math.min(totalYears, 40);
  }

  private parseExperienceYears(text: string, currentYear: number): number {
    // Extract year ranges
    const yearRangePattern = /(\d{4})\s*[-–]\s*(\d{4}|present|current)/gi;
    const matches = text.matchAll(yearRangePattern);
    
    let totalYears = 0;
    for (const match of matches) {
      const startYear = parseInt(match[1]);
      let endYear = currentYear;
      
      if (match[2] !== 'present' && match[2] !== 'current') {
        endYear = parseInt(match[2]);
      }
      
      if (startYear && endYear && endYear >= startYear) {
        totalYears += (endYear - startYear);
      }
    }
    
    // If no specific years found, look for patterns like "5 years"
    if (totalYears === 0) {
      const yearsPattern = /(\d+)\s*(?:years?|yrs?)/i;
      const yearMatch = text.match(yearsPattern);
      if (yearMatch) {
        totalYears = parseInt(yearMatch[1]);
      }
    }
    
    return totalYears;
  }
}

export const langchainService = new LangChainRAGService();