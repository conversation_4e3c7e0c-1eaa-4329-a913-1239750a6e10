export interface ExtractedData {
  name: string;
  email: string;
  phone: string;
  skills: string[];
  education: string[];
  experience: string[];
  summary: string;
  certifications: string[];
  projects: string[];
  workExperience: WorkExperience[];
  totalExperienceYears: number;
}

export interface WorkExperience {
  company: string;
  position: string;
  duration: string;
  description: string;
  startYear?: number;
  endYear?: number;
}

export interface AgentScores {
  recruiterScore: number;
  analystScore: number;
  hrScore: number;
  recommenderScore: number;
}

export interface ScoreBreakdown {
  skillsMatch: number;
  experienceMatch: number;
  educationMatch: number;
  projectMatch: number;
  communicationScore: number;
  overallFit: number;
}

export interface AgentAnalysis {
  recruiterAnalysis: string;
  analystAnalysis: string;
  hrAnalysis: string;
  recommenderAnalysis: string;
  finalRecommendation: string;
  strengths: string[];
  weaknesses: string[];
  fitScore: number;
}

export interface ResumeResult {
  id: string;
  file: File;
  extractedData: ExtractedData;
  scores: AgentScores;
  breakdown: ScoreBreakdown;
  analysis: AgentAnalysis;
  rank: number;
  isTopCandidate: boolean;
}

export interface JobDescription {
  title: string;
  company: string;
  description: string;
  requiredSkills: string[];
  preferredSkills: string[];
  experience: string;
  education: string;
  responsibilities: string[];
}

export interface AppState {
  uploadedFiles: File[];
  jobDescription: JobDescription | null;
  jobDescriptionText: string;
  results: ResumeResult[];
  isAnalyzing: boolean;
  analysisProgress: number;
  currentStep: string;
  topN: number;
  extractedContent: Map<string, string>;
}

export type AppAction =
  | { type: 'SET_UPLOADED_FILES'; payload: File[] }
  | { type: 'SET_JOB_DESCRIPTION'; payload: JobDescription }
  | { type: 'SET_JOB_DESCRIPTION_TEXT'; payload: string }
  | { type: 'SET_RESULTS'; payload: ResumeResult[] }
  | { type: 'SET_ANALYZING'; payload: boolean }
  | { type: 'SET_PROGRESS'; payload: { progress: number; step: string } }
  | { type: 'SET_TOP_N'; payload: number }
  | { type: 'SET_EXTRACTED_CONTENT'; payload: { fileId: string; content: string } }
  | { type: 'RESET_STATE' };